<template>
  
    <v-app-bar
      color="primary"
      prominent
    >
      <v-app-bar-nav-icon></v-app-bar-nav-icon>
      <v-toolbar-title>Leave Management System</v-toolbar-title>
      <v-spacer></v-spacer>
      <v-btn icon>
        <v-icon>mdi-theme-light-dark</v-icon>
      </v-btn>
      <v-btn icon>
        <v-icon>mdi-account-circle</v-icon>
      </v-btn>
    </v-app-bar>

    <v-main>
      <v-container>
        <!-- Hero Section -->
        <v-row>
          <v-col cols="12">
            <v-card color="accent" class="pa-6">
              <v-card-title class="text-h1-dashboard text-primary">
                Welcome to Leave Management System
              </v-card-title>
              <v-card-text>
                <p class="text-body-20-medium mb-4">Streamline your leave management process with our modern, intuitive platform built with TT Hoves typography.</p>
                <v-btn color="primary" variant="elevated" size="large" class="text-button-24">
                  <v-icon start>mdi-rocket-launch</v-icon>
                  Get Started
                </v-btn>
                <v-btn color="secondary" variant="outlined" size="large" class="ml-4 text-body-16-medium">
                  <v-icon start>mdi-information</v-icon>
                  Learn More
                </v-btn>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <!-- Typography Showcase -->
        <v-row class="mt-8">
          <v-col cols="12">
            <h2 class="text-h2-dashboard text-primary mb-4">TT Hoves Typography System</h2>
            <p class="text-body-18-regular mb-6">Our design system uses TT Hoves font family with multiple weights and sizes for optimal readability and visual hierarchy.</p>
          </v-col>

          <!-- Heading Examples -->
          <v-col cols="12" md="6">
            <v-card class="pa-4 mb-4">
              <v-card-title class="text-body-16-bold text-primary mb-3">Website Headings</v-card-title>
              <div class="text-h5-website mb-2">H5 Website (32px DemiBold)</div>
              <div class="text-h4-dashboard mb-2">H4 Dashboard (24px DemiBold)</div>
              <div class="text-title-1 mb-2">Title 1 (32px Medium)</div>
              <div class="text-title-2 mb-2">Title 2 (28px DemiBold)</div>
            </v-card>
          </v-col>

          <!-- Body Text Examples -->
          <v-col cols="12" md="6">
            <v-card class="pa-4 mb-4">
              <v-card-title class="text-body-16-bold text-primary mb-3">Body Text Styles</v-card-title>
              <div class="text-body-20-bold mb-2">Body 20px Bold</div>
              <div class="text-body-18-medium mb-2">Body 18px Medium</div>
              <div class="text-body-16-regular mb-2">Body 16px Regular</div>
              <div class="text-body-14-medium mb-2">Body 14px Medium</div>
              <div class="text-caption">Caption Text (16px Medium)</div>
            </v-card>
          </v-col>

          <!-- Font Weights -->
          <v-col cols="12">
            <v-card class="pa-4 mb-4">
              <v-card-title class="text-body-16-bold text-primary mb-3">Font Weight Variations</v-card-title>
              <v-row>
                <v-col cols="6" md="3">
                  <div class="text-body-18-regular font-regular">Regular (400)</div>
                </v-col>
                <v-col cols="6" md="3">
                  <div class="text-body-18-regular font-medium">Medium (500)</div>
                </v-col>
                <v-col cols="6" md="3">
                  <div class="text-body-18-regular font-demibold">DemiBold (600)</div>
                </v-col>
                <v-col cols="6" md="3">
                  <div class="text-body-18-regular font-bold">Bold (700)</div>
                </v-col>
              </v-row>
            </v-card>
          </v-col>
        </v-row>

        <!-- Color Showcase -->
        <v-row class="mt-8">
          <v-col cols="12">
            <h2 class="text-h3-dashboard text-primary mb-4">Color Palette</h2>
          </v-col>

          <!-- Primary Colors -->
          <v-col cols="12" md="3">
            <v-card color="primary" class="pa-4">
              <v-card-title class="text-white">Primary</v-card-title>
              <v-card-text class="text-white">
                Main brand color<br>
                <code>#FA4545</code>
              </v-card-text>
            </v-card>
          </v-col>

          <!-- Secondary Colors -->
          <v-col cols="12" md="3">
            <v-card color="secondary" class="pa-4">
              <v-card-title class="text-white">Secondary</v-card-title>
              <v-card-text class="text-white">
                Accent color<br>
                <code>#48A9A6</code>
              </v-card-text>
            </v-card>
          </v-col>

          <!-- Success Colors -->
          <v-col cols="12" md="3">
            <v-card color="success" class="pa-4">
              <v-card-title class="text-success-darken-1">Success</v-card-title>
              <v-card-text class="text-success-darken-1">
                Success states<br>
                <code>#CEEFDF</code>
              </v-card-text>
            </v-card>
          </v-col>

          <!-- Warning Colors -->
          <v-col cols="12" md="3">
            <v-card color="warning" class="pa-4">
              <v-card-title class="text-warning-darken-1">Warning</v-card-title>
              <v-card-text class="text-warning-darken-1">
                Warning states<br>
                <code>#FEEDDA</code>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <!-- Feature Cards -->
        <v-row class="mt-8">
          <v-col cols="12">
            <h2 class="text-h3-dashboard text-primary mb-4">Key Features</h2>
          </v-col>

          <v-col cols="12" md="4">
            <v-card elevation="2" class="h-100">
              <v-card-title class="text-body-18-bold text-primary">
                <v-icon start color="primary">mdi-calendar-check</v-icon>
                Easy Leave Requests
              </v-card-title>
              <v-card-text class="text-body-16-regular">
                Submit and track leave requests with our intuitive interface. Get real-time updates on approval status.
              </v-card-text>
              <v-card-actions>
                <v-btn color="primary" variant="text" class="text-body-14-medium">Learn More</v-btn>
              </v-card-actions>
            </v-card>
          </v-col>

          <v-col cols="12" md="4">
            <v-card elevation="2" class="h-100">
              <v-card-title class="text-body-18-bold text-secondary">
                <v-icon start color="secondary">mdi-chart-timeline-variant</v-icon>
                Analytics Dashboard
              </v-card-title>
              <v-card-text class="text-body-16-regular">
                Comprehensive analytics and reporting tools to help managers make informed decisions about team availability.
              </v-card-text>
              <v-card-actions>
                <v-btn color="secondary" variant="text" class="text-body-14-medium">View Demo</v-btn>
              </v-card-actions>
            </v-card>
          </v-col>

          <v-col cols="12" md="4">
            <v-card elevation="2" class="h-100">
              <v-card-title class="text-body-18-bold text-primary">
                <v-icon start color="primary">mdi-account-group</v-icon>
                Team Management
              </v-card-title>
              <v-card-text class="text-body-16-regular">
                Efficiently manage team schedules, approve requests, and maintain optimal staffing levels across departments.
              </v-card-text>
              <v-card-actions>
                <v-btn color="primary" variant="text" class="text-body-14-medium">Get Started</v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>

        <!-- Status Examples -->
        <v-row class="mt-8">
          <v-col cols="12">
            <h2 class="text-h4 text-primary mb-4">Status Indicators</h2>
          </v-col>

          <v-col cols="12" md="6">
            <v-alert type="success" variant="tonal">
              <v-alert-title>Leave Approved</v-alert-title>
              Your leave request has been approved by your manager.
            </v-alert>
          </v-col>

          <v-col cols="12" md="6">
            <v-alert type="warning" variant="tonal">
              <v-alert-title>Pending Review</v-alert-title>
              Your leave request is pending manager approval.
            </v-alert>
          </v-col>

          <v-col cols="12" md="6">
            <v-alert type="info" variant="tonal">
              <v-alert-title>Information</v-alert-title>
              Remember to submit your leave requests at least 2 weeks in advance.
            </v-alert>
          </v-col>

          <v-col cols="12" md="6">
            <v-alert type="error" variant="tonal">
              <v-alert-title>Request Denied</v-alert-title>
              Your leave request has been denied due to insufficient leave balance.
            </v-alert>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  
</template>
