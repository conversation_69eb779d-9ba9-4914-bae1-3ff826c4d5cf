# Leave Management System - Environment Configuration
# Copy this file to .env and update the values

# ==============================================
# APP CONFIGURATION
# ==============================================
NUXT_PUBLIC_APP_URL=http://localhost:3000
NUXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# ==============================================
# DATABASE CONFIGURATION
# ==============================================
NUXT_DATABASE_URL=postgresql://username:password@localhost:5432/leave_management_db

# ==============================================
# AUTHENTICATION & SECURITY
# ==============================================
NUXT_JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# ==============================================
# EMAIL CONFIGURATION
# ==============================================
NUXT_SMTP_HOST=smtp.gmail.com
NUXT_SMTP_PORT=587
NUXT_SMTP_USER=<EMAIL>
NUXT_SMTP_PASS=your-app-password

# ==============================================
# PUSH NOTIFICATIONS
# ==============================================
NUXT_PUBLIC_VAPID_KEY=your-vapid-public-key-for-push-notifications

# ==============================================
# INTEGRATIONS
# ==============================================
# Slack Integration
NUXT_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Microsoft Teams Integration
NUXT_TEAMS_WEBHOOK_URL=https://outlook.office.com/webhook/YOUR/TEAMS/WEBHOOK

# ==============================================
# STORAGE CONFIGURATION
# ==============================================
NUXT_STORAGE_PROVIDER=local
# For S3 storage (if using AWS S3)
NUXT_S3_BUCKET=your-s3-bucket-name
NUXT_S3_REGION=us-east-1
NUXT_S3_ACCESS_KEY=your-s3-access-key
NUXT_S3_SECRET_KEY=your-s3-secret-key

# ==============================================
# REDIS CONFIGURATION (for caching/sessions)
# ==============================================
NUXT_REDIS_URL=redis://localhost:6379

# ==============================================
# DEVELOPMENT SETTINGS
# ==============================================
NUXT_MOCK_API=true
NUXT_LOG_LEVEL=debug

# ==============================================
# PRODUCTION SETTINGS
# ==============================================
# Uncomment and set these for production
# NUXT_MOCK_API=false
# NUXT_LOG_LEVEL=error

# ==============================================
# THIRD-PARTY SERVICES
# ==============================================
# Google Calendar Integration
# NUXT_GOOGLE_CLIENT_ID=your-google-client-id
# NUXT_GOOGLE_CLIENT_SECRET=your-google-client-secret

# Microsoft Outlook Integration
# NUXT_OUTLOOK_CLIENT_ID=your-outlook-client-id
# NUXT_OUTLOOK_CLIENT_SECRET=your-outlook-client-secret

# SMS Provider (Twilio)
# NUXT_TWILIO_ACCOUNT_SID=your-twilio-account-sid
# NUXT_TWILIO_AUTH_TOKEN=your-twilio-auth-token
# NUXT_TWILIO_PHONE_NUMBER=your-twilio-phone-number

# ==============================================
# MONITORING & ANALYTICS
# ==============================================
# Sentry for error tracking
# NUXT_SENTRY_DSN=your-sentry-dsn

# Google Analytics
# NUXT_PUBLIC_GA_ID=your-google-analytics-id

# ==============================================
# NOTES
# ==============================================
# 1. Never commit the .env file to version control
# 2. Use strong, unique passwords and secrets
# 3. Rotate secrets regularly in production
# 4. Use environment-specific values for different deployments
# 5. Public variables (NUXT_PUBLIC_*) are exposed to the client
# 6. Private variables are only available on the server side
