// TT Hoves Font Family
// Based on Figma design specifications

// TT Hoves Regular (400)
@font-face {
  font-family: 'TT Hoves';
  src: url('~/assets/fonts/TTHoves-Regular.woff2') format('woff2'),
       url('~/assets/fonts/TTHoves-Regular.woff') format('woff'),
       url('~/assets/fonts/TTHoves-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

// TT Hoves Medium (500)
@font-face {
  font-family: 'TT Hoves';
  src: url('~/assets/fonts/TTHoves-Medium.woff2') format('woff2'),
       url('~/assets/fonts/TTHoves-Medium.woff') format('woff'),
       url('~/assets/fonts/TTHoves-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

// TT Hoves DemiBold (600)
@font-face {
  font-family: 'TT Hoves';
  src: url('~/assets/fonts/TTHoves-DemiBold.woff2') format('woff2'),
       url('~/assets/fonts/TTHoves-DemiBold.woff') format('woff'),
       url('~/assets/fonts/TTHoves-DemiBold.ttf') format('truetype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

// TT Hoves Bold (700)
@font-face {
  font-family: 'TT Hoves';
  src: url('~/assets/fonts/TTHoves-Bold.woff2') format('woff2'),
       url('~/assets/fonts/TTHoves-Bold.woff') format('woff'),
       url('~/assets/fonts/TTHoves-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

// Fallback fonts for better performance and accessibility
$font-family-primary: 'TT Hoves', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
$font-family-secondary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

// Typography scale based on Figma design
$typography-scale: (
  // Website Headings
  'h1-website': (
    font-family: $font-family-primary,
    font-size: 100px,
    font-weight: 600,
    line-height: 1.1,
    letter-spacing: -0.02em
  ),
  'h2-website': (
    font-family: $font-family-primary,
    font-size: 80px,
    font-weight: 600,
    line-height: 1.1,
    letter-spacing: -0.02em
  ),
  'h3-website': (
    font-family: $font-family-primary,
    font-size: 64px,
    font-weight: 600,
    line-height: 1.2,
    letter-spacing: -0.02em
  ),
  'h4-website': (
    font-family: $font-family-primary,
    font-size: 48px,
    font-weight: 600,
    line-height: 1.2,
    letter-spacing: -0.01em
  ),
  'h5-website': (
    font-family: $font-family-primary,
    font-size: 32px,
    font-weight: 600,
    line-height: 1.5
  ),
  
  // Dashboard Headings
  'h1-dashboard': (
    font-family: $font-family-primary,
    font-size: 48px,
    font-weight: 500,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'h2-dashboard': (
    font-family: $font-family-primary,
    font-size: 40px,
    font-weight: 500,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'h3-dashboard': (
    font-family: $font-family-primary,
    font-size: 32px,
    font-weight: 600,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'h4-dashboard': (
    font-family: $font-family-primary,
    font-size: 24px,
    font-weight: 600,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  
  // Body Text
  'body-24-bold': (
    font-family: $font-family-primary,
    font-size: 24px,
    font-weight: 600,
    line-height: 1.4,
    letter-spacing: -0.01em
  ),
  'body-24-medium': (
    font-family: $font-family-primary,
    font-size: 24px,
    font-weight: 500,
    line-height: 1.4,
    letter-spacing: -0.01em
  ),
  'body-24-regular': (
    font-family: $font-family-primary,
    font-size: 24px,
    font-weight: 400,
    line-height: 1.4,
    letter-spacing: -0.01em
  ),
  'body-20-bold': (
    font-family: $font-family-primary,
    font-size: 20px,
    font-weight: 600,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'body-20-medium': (
    font-family: $font-family-primary,
    font-size: 20px,
    font-weight: 500,
    line-height: 1.5
  ),
  'body-20-regular': (
    font-family: $font-family-primary,
    font-size: 20px,
    font-weight: 400,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'body-18-bold': (
    font-family: $font-family-primary,
    font-size: 18px,
    font-weight: 600,
    line-height: 1.6,
    letter-spacing: -0.01em
  ),
  'body-18-medium': (
    font-family: $font-family-primary,
    font-size: 18px,
    font-weight: 500,
    line-height: 1.6,
    letter-spacing: -0.01em
  ),
  'body-18-regular': (
    font-family: $font-family-primary,
    font-size: 18px,
    font-weight: 400,
    line-height: 1.6
  ),
  'body-16-bold': (
    font-family: $font-family-primary,
    font-size: 16px,
    font-weight: 600,
    line-height: 1.6,
    letter-spacing: -0.01em
  ),
  'body-16-medium': (
    font-family: $font-family-primary,
    font-size: 16px,
    font-weight: 500,
    line-height: 1.6,
    letter-spacing: -0.02em
  ),
  'body-16-regular': (
    font-family: $font-family-primary,
    font-size: 16px,
    font-weight: 400,
    line-height: 1.6
  ),
  'body-14-bold': (
    font-family: $font-family-primary,
    font-size: 14px,
    font-weight: 600,
    line-height: 1.6
  ),
  'body-14-medium': (
    font-family: $font-family-primary,
    font-size: 14px,
    font-weight: 500,
    line-height: 1.6
  ),
  'body-14-regular': (
    font-family: $font-family-primary,
    font-size: 14px,
    font-weight: 400,
    line-height: 1.6,
    letter-spacing: 0.01em
  ),
  'body-12-bold': (
    font-family: $font-family-primary,
    font-size: 12px,
    font-weight: 600,
    line-height: 1.6
  ),
  'body-12-medium': (
    font-family: $font-family-primary,
    font-size: 12px,
    font-weight: 500,
    line-height: 1.6
  ),
  'body-12-regular': (
    font-family: $font-family-primary,
    font-size: 12px,
    font-weight: 400,
    line-height: 1.6,
    letter-spacing: 0.01em
  ),
  
  // Button Text
  'button-24': (
    font-family: $font-family-primary,
    font-size: 24px,
    font-weight: 600,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  
  // Titles
  'title-1': (
    font-family: $font-family-primary,
    font-size: 32px,
    font-weight: 500,
    line-height: 1.3,
    letter-spacing: -0.01em
  ),
  'title-2': (
    font-family: $font-family-primary,
    font-size: 28px,
    font-weight: 600,
    line-height: 1.5
  ),
  'title-3': (
    font-family: $font-family-primary,
    font-size: 18px,
    font-weight: 600,
    line-height: 1.45,
    letter-spacing: -0.01em
  ),
  'caption': (
    font-family: $font-family-primary,
    font-size: 16px,
    font-weight: 500,
    line-height: 1.5,
    letter-spacing: -0.01em
  ),
  'caption-small': (
    font-family: $font-family-primary,
    font-size: 14px,
    font-weight: 500,
    line-height: 1.4,
    letter-spacing: -0.01em
  )
);

// Export variables for use in other files
:export {
  fontFamilyPrimary: $font-family-primary;
  fontFamilySecondary: $font-family-secondary;
}
