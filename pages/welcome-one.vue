<script setup>
definePageMeta({
  layout: 'blank',
  public: true,
});

// Use app configuration
const { app } = useAppConfig();
console.log('\x1b[41m%s\x1b[0m' ,'📌 welcome-one-9-> app =>', app);
const form = ref({
    email: '',
    password: '',
    remember: false,
})
</script>


<template>
    <a href="javascript:void(0)">
        {{ app }}
        <!-- <div class="auth-logo d-flex align-center gap-x-3">
            <VNodeRenderer :nodes="app.logo.url" />
            <h1 class="auth-title">
                {{ app.title }}
            </h1>
        </div> -->
    </a>

    <VRow no-gutters class="auth-wrapper bg-surface">
        <VCol cols="12" md="5" sm="12" xs="12" class="auth-card-v2 d-flex align-center justify-center">
            <VCard flat :max-width="460" class="mt-12 mt-sm-0 pa-6">
                <VCardText class="mb-6 pa-3">
                    <h3 class="text-h2 mb-1 font-weight-bold">
                        Welcome to Leave 👋
                    </h3>
                    <p class="mb-0 text-primary">
                        Step 1 of 3
                    </p>
                </VCardText>
                <VCardText class="pa-3">
                    <VForm @submit.prevent="() => { }">
                        <VRow>
                            <!-- name -->
                            <VCol cols="12" class="pa-2">
                                <VTextField variant="outlined" v-model="form.name" autofocus label="Your Name" type="text"
                                    outline placeholder="Jonas Khanwald" class="text-primary" />
                            </VCol>

                            <!-- organization name -->
                            <VCol cols="12" class="pa-2">
                                <VTextField variant="outlined" v-model="form.organization" autofocus label="Organization Name" type="text"
                                    placeholder="Organization Name" class="text-primary" />
                            </VCol>

                            <!-- time zone -->
                            <VCol cols="12" class="pa-2">
                                <VSelect variant="outlined" v-model="form.timezone" :items="timeZones" label="Time Zone"
                                    placeholder="Select a time zone" class="mb-2 text-primary" />

                                <div>
                                    <p color="secondary">Notifications are sent according to this timezone</p>
                                </div>

                                <VBtn block type="submit" color="primary" class="my-6">
                                    Continue
                                </VBtn>
                            </VCol>

                            <!-- create account -->
                            <VCol cols="12"
                                class="text-body-1 text-center text-primary align-center justify-center items-center d-flex">
                                <span class="d-inline-block">
                                    Have a doubt?
                                </span>
                                <a class="text-primary ms-1 d-inline-block text-body-1 font-weight-medium align-center d-flex"
                                    href="javascript:void(0)">
                                    Chat with us
                                    <!-- <svg xmlns="http://www.w3.org/2000/svg" height="10px" width="10px" class="ms-1"
                                        viewBox="0 0 347.34 347.34" xml:space="preserve">
                                        <path
                                            d="M162.634 0v30h133.493L0 326.127l21.213 21.213L317.34 51.213v133.493h30V0z" />
                                    </svg> -->

                                    <svg width="20" height="20" viewBox="0 0 15 15" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M3.646 11.354a.5.5 0 0 1 0-.707L10.293 4H6a.5.5 0 0 1 0-1h5.5a.5.5 0 0 1 .5.5V9a.5.5 0 0 1-1 0V4.707l-6.646 6.647a.5.5 0 0 1-.708 0"
                                            fill="#7367F0" />
                                    </svg>
                                </a>
                            </VCol>
                        </VRow>
                    </VForm>
                </VCardText>
            </VCard>
        </VCol>

        <VCol cols="12" md="7" sm="0" class="d-none d-md-flex">
            <div class="position-relative bg-background w-100 me-0">
                <div class="d-flex align-center justify-center w-100 h-100" style="padding-inline: 6.25rem;">
                    <!-- <VImg
                max-width="613"
                :src="authThemeImg"
                class="auth-illustration mt-16 mb-2"
              /> -->
                </div>

                <!-- <img
              class="auth-footer-mask flip-in-rtl"
              :src="authThemeMask"
              alt="auth-footer-mask"
              height="280"
              width="100"
            > -->
            </div>
        </VCol>
    </VRow>
</template>

<style lang="scss">
@use '@/assets/pages/page-auth.scss';
</style>
