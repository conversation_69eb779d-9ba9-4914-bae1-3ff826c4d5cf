<template>
  <div class="calendar-sidebar">
    <!-- Date Navigation -->
    <v-card class="date-nav-card mb-6" elevation="2" rounded="15">
      <v-card-text class="pa-4">
        <div class="date-nav-header">
          <v-btn icon size="small" variant="text">
            <v-icon color="#ADB8CC">mdi-chevron-left</v-icon>
          </v-btn>
          
          <h3 class="date-title">December 2024</h3>
          
          <v-btn icon size="small" variant="text">
            <v-icon color="#ADB8CC">mdi-chevron-right</v-icon>
          </v-btn>
        </div>
        
        <!-- Mini Calendar -->
        <div class="mini-calendar mt-4">
          <div class="calendar-weekdays">
            <div v-for="day in weekdays" :key="day" class="weekday">
              {{ day }}
            </div>
          </div>
          
          <div class="calendar-dates">
            <div 
              v-for="date in calendarDates" 
              :key="date.id"
              class="calendar-date"
              :class="{ 
                'active': date.active, 
                'other-month': date.otherMonth,
                'today': date.today 
              }"
            >
              {{ date.day }}
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- Team Members -->
    <v-card class="team-card" elevation="2" rounded="15">
      <v-card-text class="pa-4">
        <div class="team-header">
          <h3 class="team-title">Team Members</h3>
          <v-btn icon size="small" variant="text">
            <v-icon color="#ADB8CC" size="16">mdi-plus</v-icon>
          </v-btn>
        </div>
        
        <div class="team-list mt-4">
          <div 
            v-for="member in teamMembers" 
            :key="member.id"
            class="team-member"
          >
            <div class="member-info">
              <v-avatar size="34" class="member-avatar">
                <v-img :src="member.avatar" />
              </v-avatar>
              
              <div class="member-details">
                <div class="member-name">{{ member.name }}</div>
                <div class="member-role">{{ member.role }}</div>
              </div>
            </div>
            
            <div class="member-status">
              <v-chip 
                :color="member.statusColor" 
                size="small" 
                variant="flat"
                class="status-chip"
              >
                {{ member.status }}
              </v-chip>
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const weekdays = ref(['S', 'M', 'T', 'W', 'T', 'F', 'S'])

const calendarDates = ref([
  // Previous month dates
  { id: 1, day: 29, otherMonth: true },
  { id: 2, day: 30, otherMonth: true },
  
  // Current month dates
  { id: 3, day: 1 },
  { id: 4, day: 2 },
  { id: 5, day: 3 },
  { id: 6, day: 4 },
  { id: 7, day: 5 },
  { id: 8, day: 6 },
  { id: 9, day: 7 },
  { id: 10, day: 8 },
  { id: 11, day: 9 },
  { id: 12, day: 10 },
  { id: 13, day: 11 },
  { id: 14, day: 12, active: true },
  { id: 15, day: 13 },
  { id: 16, day: 14 },
  { id: 17, day: 15, today: true },
  { id: 18, day: 16 },
  { id: 19, day: 17 },
  { id: 20, day: 18 },
  { id: 21, day: 19 },
  { id: 22, day: 20 },
  { id: 23, day: 21 },
  { id: 24, day: 22 },
  { id: 25, day: 23 },
  { id: 26, day: 24 },
  { id: 27, day: 25 },
  
  // Next month dates
  { id: 28, day: 1, otherMonth: true },
  { id: 29, day: 2, otherMonth: true },
  { id: 30, day: 3, otherMonth: true },
  { id: 31, day: 4, otherMonth: true },
  { id: 32, day: 5, otherMonth: true }
])

const teamMembers = ref([
  {
    id: 1,
    name: 'Sarah Johnson',
    role: 'Product Manager',
    avatar: 'https://via.placeholder.com/34x34/4A90E2/FFFFFF?text=SJ',
    status: 'Available',
    statusColor: 'success'
  },
  {
    id: 2,
    name: 'Mike Chen',
    role: 'Developer',
    avatar: 'https://via.placeholder.com/34x34/FF6B6B/FFFFFF?text=MC',
    status: 'In Meeting',
    statusColor: 'warning'
  },
  {
    id: 3,
    name: 'Emma Wilson',
    role: 'Designer',
    avatar: 'https://via.placeholder.com/34x34/26DE81/FFFFFF?text=EW',
    status: 'On Leave',
    statusColor: 'error'
  },
  {
    id: 4,
    name: 'David Brown',
    role: 'QA Engineer',
    avatar: 'https://via.placeholder.com/34x34/FD79A8/FFFFFF?text=DB',
    status: 'Available',
    statusColor: 'success'
  }
])
</script>

<style scoped>
.calendar-sidebar {
  height: 100%;
}

.date-nav-card {
  background: white;
  box-shadow: 0px 2px 4px 0px rgba(15, 28, 51, 0.06), 0px 2px 2px 0px rgba(15, 28, 51, 0.07);
}

.date-nav-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.date-title {
  font-size: 20px;
  font-weight: 500;
  color: #404040;
}

.mini-calendar {
  width: 100%;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8px;
  margin-bottom: 12px;
}

.weekday {
  text-align: center;
  font-size: 13px;
  font-weight: 700;
  color: #4D5E80;
  padding: 4px;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
}

.calendar-date {
  text-align: center;
  padding: 8px 4px;
  font-size: 13px;
  font-weight: 700;
  color: #C3C9D6;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.calendar-date:hover {
  background-color: #F5F5F5;
}

.calendar-date:not(.other-month) {
  color: #232323;
}

.calendar-date.active {
  background-color: #FA4545;
  color: white;
}

.calendar-date.today {
  background-color: #FFF3E0;
  color: #FA4545;
  font-weight: 900;
}

.team-card {
  background: white;
  box-shadow: 0px 2px 4px 0px rgba(15, 28, 51, 0.06), 0px 2px 2px 0px rgba(15, 28, 51, 0.07);
}

.team-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.team-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.team-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.team-member {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
}

.member-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.member-avatar {
  border: 2px solid #F5F5F5;
}

.member-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.member-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.member-role {
  font-size: 14px;
  font-weight: 500;
  color: #ADB8CC;
  line-height: 1.2;
}

.member-status {
  flex-shrink: 0;
}

.status-chip {
  font-size: 12px;
  font-weight: 500;
  height: 24px;
}
</style>
