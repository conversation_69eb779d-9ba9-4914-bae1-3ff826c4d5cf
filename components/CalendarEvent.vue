<template>
  <div class="calendar-event" :style="{ backgroundColor: event.color }">
    <div class="event-content">
      <v-avatar size="20" class="event-avatar">
        <v-img :src="event.avatar" />
      </v-avatar>
      <span class="event-name">{{ event.name }}</span>
    </div>
  </div>
</template>

<script setup>
defineProps({
  event: {
    type: Object,
    required: true
  }
})
</script>

<style scoped>
.calendar-event {
  border-radius: 6px;
  padding: 8px 12px;
  margin: 2px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.calendar-event:hover {
  transform: translateY(-1px);
  box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.15);
}

.event-content {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 100%;
}

.event-avatar {
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.event-name {
  font-size: 12px;
  font-weight: 500;
  color: white;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
