<script setup>
import signInImage from '@images/illustrations/signin.png';
import { VNodeRenderer } from '@layouts/components/VNodeRenderer';
import { themeConfig } from '@themeConfig';
definePageMeta({
  layout: 'blank',
  public: true,
})

const currentStep = ref(1)

const form = ref({
  name: '',
  organization: '',
  timezone: '',
  leaveCycle: '',
  leaveType: '',
  emoji: '',
  creditType: '',
  yearlyLeaves: 20,
  enableHalfDay: false
})

const timeZones = ref([])

const leaveCycleOptions = [
  { label: 'January - December', value: 'jan-dec' },
  { label: 'April - March', value: 'apr-mar' },
  { label: 'Choose a different leave year', value: 'custom' }
]

const creditTypeOptions = [
  { label: 'Credit yearly', value: 'yearly' },
  { label: 'Credit monthly', value: 'monthly' },
  { label: 'Unlimited leaves', value: 'unlimited' }
]

const handleBack = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}

const handleContinue = () => {
  if (currentStep.value < 3) {
    currentStep.value++
  }
}
</script>

<template>
  <a href="javascript:void(0)">
    <div class="auth-logo d-flex align-center gap-x-3">
      <VNodeRenderer :nodes="themeConfig.app.logo" />
      <h1 class="auth-title d-none">
        {{ themeConfig.app.title }}
      </h1>
    </div>
  </a>

  <VRow no-gutters class="auth-wrapper bg-surface">
    <VCol cols="12" md="6" sm="12" xs="12" class="auth-card-v2 d-flex align-center justify-center">
      <VCard flat :max-width="460" class="mt-12 mt-sm-0 pa-6">
        <!-- Back button for steps 2 and 3 -->
        <div v-if="currentStep > 1" class="text-primary d-flex cursor-pointer" @click="handleBack">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" xml:space="preserve" height="20px" width="20px"
            class="me-1">
            <path fill="none" stroke="#7367F0" stroke-width="2" stroke-miterlimit="10"
              d="M6 16h22m-14 8.5L5.5 16 14 7.5" />
          </svg>
          <p>Back</p>
        </div>

        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 1">
          <VCardText class="mb-6 pa-3">
            <h3 class="text-h3 mb-1 font-weight-bold font-secondary">
              Welcome to Leave 👋
            </h3>
            <p class="mb-0 text-primary">
              Step 1 of 3
            </p>
          </VCardText>
          <VCardText class="pa-3">
            <VForm @submit.prevent="handleContinue">
              <VRow>
                <VCol cols="12" class="pa-2">
                  <TextField v-model="form.name" autofocus label="Your Name" type="text" placeholder="Jonas Khanwald"
                    class="text-primary" />
                </VCol>

                <VCol cols="12" class="pa-2">
                  <TextField v-model="form.organization" label="Organization Name" type="text"
                    placeholder="Organization Name" class="text-primary" />
                </VCol>

                <VCol cols="12" class="pa-2">
                  <VSelect v-model="form.timezone" :items="timeZones" label="Time Zone" placeholder="Select a time zone"
                    class="mb-2" />
                  <div>
                    <p color="secondary">Notifications are sent according to this timezone</p>
                  </div>
                  <VBtn block type="submit" color="primary" class="my-6">
                    Continue
                  </VBtn>
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>

        <!-- Step 2: Leave Cycle -->
        <div v-if="currentStep === 2">
          <VCardText class="mb-6 pa-3">
            <h3 class="text-h3 mb-1 font-weight-bold font-secondary">

              What is your annual leave cycle? 🗓️
            </h3>
            <p class="mb-0 text-primary">
              Step 2 of 3
            </p>
          </VCardText>
          <VCardText class="pa-3">
            <VForm @submit.prevent="handleContinue">
              <VRow>
                <VCol cols="12" class="pa-0">
                  <VRadioGroup v-model="form.leaveCycle">
                    <VRadio v-for="option in leaveCycleOptions" :key="option.value" :label="option.label"
                      :value="option.value" :class="form.leaveCycle === option.value ? 'text-primary' : ''" />
                  </VRadioGroup>
                </VCol>

                <VCol cols="12" class="pa-2">
                  <div>
                    <p color="secondary" class="">Your leave cycle determines when leaves
                      renew and carry forward. Please ensure you select the correct cycle. Changing
                      this setting later would require us to reset the entire system for you</p>
                  </div>
                  <VBtn block type="submit" color="primary" class="my-6">
                    Continue
                  </VBtn>
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>

        <!-- Step 3: Leave Policy -->
        <div v-if="currentStep === 3">
          <VCardText class="mb-3 pa-3">
            <h3 class="text-h3 mb-1 font-weight-bold font-secondary">

              Set up your leave policy
            </h3>
            <p class="mb-0 text-primary">
              Step 3 of 3
            </p>
          </VCardText>
          <VCardText class="pa-3">
            <VForm @submit.prevent="handleContinue">
              <h6 class="text-h6 mb-2 font-weight-medium">Create leaves type</h6>
              <VRow>
                <VCol cols="12">
                  <TextField v-model="form.leaveType" autofocus label="Leave Type name" type="text"
                    placeholder="Annual Leave" class="text-primary" />
                </VCol>

                <VCol cols="8">
                  <VRadioGroup v-model="form.creditType">
                    <VRadio v-for="option in creditTypeOptions" :key="option.value" :label="option.label"
                      :value="option.value" :class="form.creditType === option.value ? 'text-primary' : ''" />
                  </VRadioGroup>
                </VCol>
                <VCol cols="4" class="pe-4">
                  <VTextField v-model="form.yearlyLeaves" label="Every year" type="number" placeholder="20"
                    class="text-primary" />
                </VCol>

                <VCol cols="12">
                  <VRow class="mt-0">
                    <VCol col="12" class="pa-3 d-flex justify-space-between align-center">

                      <p class="mb-0">Enable/Disable Half Day Leave</p>
                      <VSwitch v-model="form.enableHalfDay" color="primary" />
                    </VCol>
                  </VRow>
                  <VBtn block type="submit" color="primary" class="my-2">
                    Create leaves type
                  </VBtn>
                </VCol>
              </VRow>
            </VForm>
          </VCardText>
        </div>

        <!-- Common Footer -->
        <VCol cols="12" class="text-body-1 text-center text-primary align-center justify-center items-center d-flex">
          <span class="d-inline-block">
            Have a doubt?
          </span>
          <a class="text-primary ms-1 d-inline-block text-body-1 font-weight-medium align-center d-flex"
            href="javascript:void(0)">
            Chat with us
            <svg width="20" height="20" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd"
                d="M3.646 11.354a.5.5 0 0 1 0-.707L10.293 4H6a.5.5 0 0 1 0-1h5.5a.5.5 0 0 1 .5.5V9a.5.5 0 0 1-1 0V4.707l-6.646 6.647a.5.5 0 0 1-.708 0"
                fill="#7367F0" />
            </svg>
          </a>
        </VCol>
      </VCard>
    </VCol>

    <VCol cols="12" md="6" sm="0" class="d-none d-md-flex">
      <div class="position-relative bg-secondary-light w-100 me-0">
        <div class="d-flex align-center justify-center w-100 h-100" style="padding-inline: 6.25rem;">
          <VImg max-width="613" :src="signInImage" class="auth-illustration mt-16 mb-2" />
        </div>


      </div>
    </VCol>
  </VRow>
</template>

<style lang="scss">
@use "@core/scss/template/pages/page-auth";
</style>
