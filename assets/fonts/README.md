# TT Hoves Font Files

This directory should contain the TT Hoves font files in the following formats:

## Required Font Files

### Regular (400)
- `TTHoves-Regular.woff2`
- `TTHoves-Regular.woff`
- `TTHoves-Regular.ttf`

### Medium (500)
- `TTHoves-Medium.woff2`
- `TTHoves-Medium.woff`
- `TTHoves-Medium.ttf`

### DemiBold (600)
- `TTHoves-DemiBold.woff2`
- `TTHoves-DemiBold.woff`
- `TTHoves-DemiBold.ttf`

### Bold (700)
- `TTHoves-Bold.woff2`
- `TTHoves-Bold.woff`
- `TTHoves-Bold.ttf`

## How to Obtain Font Files

1. **Purchase from TypeType**: TT <PERSON><PERSON> is a commercial font available from TypeType foundry
2. **Download from Figma**: If you have access to the design file, you may be able to export the fonts
3. **Contact Design Team**: Ask your design team for the font files

## Alternative Fonts

If TT Hoves is not available, the system will fall back to:
- -apple-system (macOS)
- BlinkMacSystemFont (macOS)
- Segoe UI (Windows)
- Robot<PERSON> (Android)
- Helvetica Neue
- Arial
- sans-serif

## Font Loading

The fonts are configured with `font-display: swap` for optimal performance:
- Text will be displayed immediately with fallback fonts
- TT Hoves will replace fallback fonts when loaded
- No layout shift or invisible text during font loading

## Usage

Once the font files are placed in this directory, they will be automatically loaded and applied throughout the application via the typography system defined in `assets/typography.scss`.
