<script setup>
definePageMeta({
  layout: 'default',
});

// Use app configuration
const { app } = useLeaveConfig();

// Sample members data
const members = ref([
  {
    id: 1,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    avatar: null,
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    email: '<EMAIL>',
    role: 'Admin',
    avatar: null,
  },
]);

// Search and filter states
const searchQuery = ref('');
const selectedTeam = ref('All Teams');
const selectedApprover = ref('All Approvers');

// Sample data for dropdowns
const teams = ['All Teams', 'Development', 'Design', 'Marketing', 'HR'];
const approvers = ['All Approvers', 'Manager', 'HR Lead', 'Department Head'];

// Methods
const inviteMembers = () => {
  console.log('Invite members clicked');
};

const handleMemberAction = (memberId, action) => {
  console.log(`Action ${action} for member ${memberId}`);
};
</script>

<template>
  <v-app>
    <!-- Navigation Header -->
    <v-app-bar 
      color="white" 
      elevation="0" 
      height="80"
      class="border-b"
    >
      <v-container fluid class="px-6">
        <v-row align="center" justify="space-between" no-gutters>
          <!-- Logo Section -->
          <v-col cols="auto">
            <div class="d-flex align-center gap-3">
              <div class="d-flex align-center gap-2">
                <v-icon color="primary" size="32">{{ app.logo.icon }}</v-icon>
                <span class="text-h5-dashboard text-primary font-demibold">Leave</span>
              </div>
              <v-chip 
                color="grey-100" 
                text-color="grey-700"
                size="small"
                class="text-body-14-medium"
              >
                Teams
              </v-chip>
            </div>
          </v-col>

          <!-- Search and User Section -->
          <v-col cols="auto">
            <div class="d-flex align-center gap-4">
              <!-- Global Search -->
              <v-text-field
                placeholder="Search …"
                prepend-inner-icon="mdi-magnify"
                variant="outlined"
                density="compact"
                hide-details
                class="search-field"
                style="width: 300px;"
                rounded
              />
              
              <!-- Location Selector -->
              <v-btn
                variant="text"
                class="text-body-16-regular text-grey-700"
                append-icon="mdi-chevron-down"
              >
                <v-icon start>mdi-map-marker</v-icon>
                New York
              </v-btn>

              <!-- User Profile -->
              <v-btn
                variant="text"
                class="text-body-16-regular text-grey-700"
              >
                <v-icon start>mdi-account-circle</v-icon>
                Adrain Nader
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- Main Content -->
    <v-main class="bg-grey-50">
      <v-container fluid class="pa-6">
        <v-row>
          <!-- Main Content Area -->
          <v-col cols="12" lg="8">
            <!-- Page Header -->
            <div class="mb-6">
              <div class="d-flex align-center justify-space-between mb-4">
                <div>
                  <h1 class="text-h1-dashboard text-grey-900 mb-2">All members</h1>
                  
                  <!-- Alert Banner -->
                  <v-alert
                    type="error"
                    variant="tonal"
                    density="compact"
                    class="mb-4"
                    style="max-width: 400px;"
                  >
                    <template #prepend>
                      <v-icon>mdi-alert-circle</v-icon>
                    </template>
                    <span class="text-body-16-medium">1 person has no holidays assigned</span>
                  </v-alert>
                </div>

                <!-- Invite Members Button -->
                <v-btn
                  color="primary"
                  size="large"
                  class="text-body-16-bold"
                  style="height: 46px; padding: 0 29px;"
                  @click="inviteMembers"
                >
                  Invite Members
                </v-btn>
              </div>

              <!-- Search and Filters -->
              <div class="d-flex gap-4 mb-6">
                <!-- Search Members -->
                <v-text-field
                  v-model="searchQuery"
                  placeholder="Search for members"
                  prepend-inner-icon="mdi-magnify"
                  variant="outlined"
                  density="comfortable"
                  hide-details
                  style="width: 560px;"
                />

                <!-- All Teams Filter -->
                <v-select
                  v-model="selectedTeam"
                  :items="teams"
                  variant="outlined"
                  density="comfortable"
                  hide-details
                  style="width: 260px;"
                  append-icon="mdi-chevron-down"
                />

                <!-- All Approvers Filter -->
                <v-select
                  v-model="selectedApprover"
                  :items="approvers"
                  variant="outlined"
                  density="comfortable"
                  hide-details
                  style="width: 260px;"
                  append-icon="mdi-chevron-down"
                />
              </div>
            </div>

            <!-- Members Table -->
            <v-card elevation="1" class="rounded-lg">
              <!-- Table Header -->
              <div class="table-header bg-grey-100 pa-4">
                <v-row align="center" no-gutters>
                  <v-col cols="auto" class="mr-4">
                    <v-checkbox
                      density="compact"
                      hide-details
                    />
                  </v-col>
                  <v-col cols="2">
                    <span class="text-body-14-medium text-grey-700">Members</span>
                  </v-col>
                  <v-col cols="3">
                    <!-- Empty for spacing -->
                  </v-col>
                  <v-col cols="2" class="text-center">
                    <span class="text-body-14-medium text-grey-700">Teams</span>
                  </v-col>
                  <v-col cols="3" class="text-center">
                    <span class="text-body-14-medium text-grey-700">Approvers</span>
                  </v-col>
                  <v-col cols="2">
                    <!-- Empty for actions -->
                  </v-col>
                </v-row>
              </div>

              <v-divider />

              <!-- Member Rows -->
              <div v-for="(member, index) in members" :key="member.id">
                <div class="pa-4">
                  <v-row align="center" no-gutters>
                    <!-- Checkbox -->
                    <v-col cols="auto" class="mr-4">
                      <v-checkbox
                        density="compact"
                        hide-details
                      />
                    </v-col>

                    <!-- Member Info -->
                    <v-col cols="5">
                      <div class="d-flex align-center gap-3">
                        <!-- Avatar -->
                        <v-avatar
                          size="46"
                          color="grey-100"
                        >
                          <v-icon color="grey-600">mdi-account</v-icon>
                        </v-avatar>

                        <!-- Name and Email -->
                        <div>
                          <div class="d-flex align-center gap-2 mb-1">
                            <span class="text-body-17-medium text-grey-900">{{ member.name }}</span>
                            <v-chip
                              size="small"
                              color="grey-300"
                              text-color="white"
                              class="text-caption-small"
                            >
                              {{ member.role }}
                            </v-chip>
                          </div>
                          <div class="text-body-15-regular text-grey-600">{{ member.email }}</div>
                        </div>
                      </div>
                    </v-col>

                    <!-- Teams -->
                    <v-col cols="2" class="text-center">
                      <span class="text-body-16-regular text-grey-900">Add teams</span>
                    </v-col>

                    <!-- Approvers -->
                    <v-col cols="3" class="text-center">
                      <v-btn
                        variant="text"
                        size="small"
                        append-icon="mdi-chevron-down"
                        class="text-body-16-regular text-grey-900"
                      >
                        {{ member.name }}
                      </v-btn>
                    </v-col>

                    <!-- Actions -->
                    <v-col cols="2" class="text-right">
                      <v-menu>
                        <template #activator="{ props }">
                          <v-btn
                            icon
                            variant="text"
                            size="small"
                            v-bind="props"
                          >
                            <v-icon>mdi-dots-horizontal-circle</v-icon>
                          </v-btn>
                        </template>
                        <v-list>
                          <v-list-item @click="handleMemberAction(member.id, 'edit')">
                            <v-list-item-title>Edit</v-list-item-title>
                          </v-list-item>
                          <v-list-item @click="handleMemberAction(member.id, 'delete')">
                            <v-list-item-title>Delete</v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </v-col>
                  </v-row>
                </div>
                
                <v-divider v-if="index < members.length - 1" />
              </div>
            </v-card>
          </v-col>

          <!-- Sidebar Menu -->
          <v-col cols="12" lg="4">
            <div class="pl-6">
              <v-card elevation="1" class="rounded-lg">
                <!-- All Members Menu Item -->
                <v-list-item
                  class="pa-4"
                  style="border-bottom: 1px solid #E5E5E5;"
                >
                  <template #prepend>
                    <v-icon color="grey-600">mdi-account-circle</v-icon>
                  </template>
                  
                  <v-list-item-title class="d-flex justify-space-between align-center">
                    <span class="text-body-17-medium text-grey-700">All members</span>
                    <span class="text-body-17-medium text-grey-700">02</span>
                  </v-list-item-title>
                </v-list-item>

                <!-- Teams Menu Item -->
                <v-list-item class="pa-4">
                  <template #prepend>
                    <v-icon color="grey-600">mdi-account-group</v-icon>
                  </template>
                  
                  <v-list-item-title class="d-flex justify-space-between align-center">
                    <span class="text-body-17-medium text-grey-700">Teams</span>
                    <span class="text-body-17-medium text-grey-700">05</span>
                  </v-list-item-title>
                </v-list-item>
              </v-card>
            </div>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-app>
</template>

<style scoped>
.search-field :deep(.v-field) {
  border-radius: 35px;
}

.table-header {
  border-radius: 8px 8px 0 0;
}

.border-b {
  border-bottom: 1px solid #E5E5E5;
}

/* Custom typography classes that might not be in our system yet */
.text-body-17-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 17px;
  font-weight: 500;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-body-15-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 15px;
  font-weight: 400;
  line-height: 1.2;
  letter-spacing: -0.01em;
}
</style>
