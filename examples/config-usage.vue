<template>
  <div class="config-example pa-6">
    <!-- App Information -->
    <v-card class="mb-6">
      <v-card-title class="text-h3-dashboard text-primary">
        App Configuration Example
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">App Information</h4>
            <p><strong>Name:</strong> {{ app.name }}</p>
            <p><strong>Title:</strong> {{ app.title }}</p>
            <p><strong>Version:</strong> {{ app.version }}</p>
            <p><strong>Description:</strong> {{ app.description }}</p>
            <p><strong>URL:</strong> {{ app.url }}</p>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Logo & Branding</h4>
            <div class="d-flex align-center gap-3 mb-3">
              <v-icon :color="getThemeColor('primary')" size="32">{{ app.logo.icon }}</v-icon>
              <span class="text-body-16-medium">{{ app.logo.text }}</span>
            </div>
            <p><strong>Author:</strong> {{ app.author.name }}</p>
            <p><strong>Email:</strong> {{ app.author.email }}</p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Theme Configuration -->
    <v-card class="mb-6">
      <v-card-title class="text-h4-dashboard text-primary">
        Theme Configuration
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Colors</h4>
            <div class="color-grid">
              <div v-for="(color, name) in theme.colors" :key="name" class="color-item mb-2">
                <div 
                  class="color-swatch" 
                  :style="{ backgroundColor: color }"
                ></div>
                <span class="text-body-14-regular">{{ name }}: {{ color }}</span>
              </div>
            </div>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Typography</h4>
            <p><strong>Primary Font:</strong> {{ getFontFamily('primary') }}</p>
            <p><strong>Secondary Font:</strong> {{ getFontFamily('secondary') }}</p>
            <div class="typography-examples mt-4">
              <div class="text-h1-dashboard mb-2">H1 Dashboard ({{ getTypographySize('h1-dashboard') }})</div>
              <div class="text-h2-dashboard mb-2">H2 Dashboard ({{ getTypographySize('h2-dashboard') }})</div>
              <div class="text-body-large mb-2">Body Large ({{ getTypographySize('body-large') }})</div>
              <div class="text-body-medium mb-2">Body Medium ({{ getTypographySize('body-medium') }})</div>
              <div class="text-caption">Caption ({{ getTypographySize('caption') }})</div>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Features Configuration -->
    <v-card class="mb-6">
      <v-card-title class="text-h4-dashboard text-primary">
        Features & Capabilities
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="4">
            <h4 class="text-body-18-bold mb-3">Core Features</h4>
            <v-list density="compact">
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.multiTenant ? 'success' : 'error'">
                    {{ features.multiTenant ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Multi-Tenant</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.analytics ? 'success' : 'error'">
                    {{ features.analytics ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Analytics</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.reporting ? 'success' : 'error'">
                    {{ features.reporting ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Reporting</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.approvalWorkflow ? 'success' : 'error'">
                    {{ features.approvalWorkflow ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Approval Workflow</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-col>
          <v-col cols="12" md="4">
            <h4 class="text-body-18-bold mb-3">Notifications</h4>
            <v-list density="compact">
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.notifications.email ? 'success' : 'error'">
                    {{ features.notifications.email ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Email</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.notifications.push ? 'success' : 'error'">
                    {{ features.notifications.push ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Push</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.notifications.sms ? 'success' : 'error'">
                    {{ features.notifications.sms ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>SMS</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-col>
          <v-col cols="12" md="4">
            <h4 class="text-body-18-bold mb-3">Integrations</h4>
            <v-list density="compact">
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.integrations.calendar ? 'success' : 'error'">
                    {{ features.integrations.calendar ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Calendar</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.integrations.slack ? 'success' : 'error'">
                    {{ features.integrations.slack ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Slack</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon :color="features.integrations.teams ? 'success' : 'error'">
                    {{ features.integrations.teams ? 'mdi-check' : 'mdi-close' }}
                  </v-icon>
                </template>
                <v-list-item-title>Teams</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Leave Types Configuration -->
    <v-card class="mb-6">
      <v-card-title class="text-h4-dashboard text-primary">
        Leave Types Configuration
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col v-for="leaveType in leave.types" :key="leaveType.id" cols="12" md="4">
            <v-card variant="outlined" class="h-100">
              <v-card-text>
                <div class="d-flex align-center gap-3 mb-3">
                  <v-icon :color="leaveType.color" size="24">{{ leaveType.icon }}</v-icon>
                  <span class="text-body-16-bold">{{ leaveType.name }}</span>
                </div>
                <p class="text-body-14-regular mb-2">
                  <strong>Max Days:</strong> {{ leaveType.maxDays || 'Unlimited' }}
                </p>
                <p class="text-body-14-regular mb-2">
                  <strong>Carry Over:</strong> {{ leaveType.carryOver ? 'Yes' : 'No' }}
                </p>
                <v-chip :color="leaveType.color" size="small" variant="tonal">
                  {{ leaveType.id }}
                </v-chip>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- API Configuration -->
    <v-card class="mb-6">
      <v-card-title class="text-h4-dashboard text-primary">
        API Configuration
      </v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Base Configuration</h4>
            <p><strong>Base URL:</strong> {{ api.baseUrl }}</p>
            <p><strong>Timeout:</strong> {{ api.timeout }}ms</p>
            <p><strong>Retries:</strong> {{ api.retries }}</p>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Helper Functions</h4>
            <p><strong>Auth Login URL:</strong> {{ getApiUrl(api.endpoints.auth.login) }}</p>
            <p><strong>Leave Requests URL:</strong> {{ getApiUrl(api.endpoints.leave.requests) }}</p>
            <p><strong>Users List URL:</strong> {{ getApiUrl(api.endpoints.users.list) }}</p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Development Configuration -->
    <v-card v-if="isDevelopment">
      <v-card-title class="text-h4-dashboard text-warning">
        Development Configuration
      </v-card-title>
      <v-card-text>
        <v-alert type="info" class="mb-4">
          This section is only visible in development mode.
        </v-alert>
        <v-row>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Environment</h4>
            <p><strong>Debug Mode:</strong> {{ development.debug ? 'Enabled' : 'Disabled' }}</p>
            <p><strong>Mock API:</strong> {{ development.mockApi ? 'Enabled' : 'Disabled' }}</p>
            <p><strong>Dev Tools:</strong> {{ development.showDevTools ? 'Enabled' : 'Disabled' }}</p>
            <p><strong>Log Level:</strong> {{ development.logLevel }}</p>
          </v-col>
          <v-col cols="12" md="6">
            <h4 class="text-body-18-bold mb-3">Helper Functions</h4>
            <p><strong>Is Development:</strong> {{ isDevelopment }}</p>
            <p><strong>Is Mock API:</strong> {{ isMockApiEnabled }}</p>
            <p><strong>Environment Config:</strong></p>
            <pre class="text-body-12-regular">{{ JSON.stringify(getEnvironmentConfig(), null, 2) }}</pre>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
// Import the configuration composable
const {
  app,
  theme,
  api,
  features,
  leave,
  development,
  getApiUrl,
  getThemeColor,
  getFontFamily,
  getTypographySize,
  isDevelopment,
  isMockApiEnabled,
  getEnvironmentConfig,
} = useLeaveConfig();
</script>

<style scoped>
.color-grid {
  display: grid;
  gap: 8px;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.color-swatch {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.typography-examples > div {
  border-left: 3px solid #FA4545;
  padding-left: 12px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
}
</style>
