<template>
  <v-card class="calendar-container" elevation="6" rounded="20">
    <v-card-text class="pa-0">
      <!-- Calendar Header with Days -->
      <div class="calendar-header">
        <!-- Empty corner for time column -->
        <div class="time-header"></div>
        
        <!-- Day Headers -->
        <div 
          v-for="day in days" 
          :key="day.name"
          class="day-header"
        >
          <div class="day-name">{{ day.name }}</div>
          <div class="day-date">{{ day.date }}</div>
        </div>
      </div>

      <!-- Calendar Body -->
      <div class="calendar-body">
        <!-- Time Column -->
        <div class="time-column">
          <div 
            v-for="hour in timeSlots" 
            :key="hour"
            class="time-slot"
          >
            <span class="time-text">{{ hour }}</span>
            <v-icon size="16" color="#ADB8CC" class="time-icon">mdi-clock-outline</v-icon>
          </div>
        </div>

        <!-- Calendar Grid -->
        <div class="calendar-grid">
          <!-- Grid Lines -->
          <div class="grid-lines">
            <!-- Horizontal lines -->
            <div 
              v-for="i in timeSlots.length" 
              :key="`h-${i}`"
              class="grid-line horizontal"
              :style="{ top: `${(i - 1) * 60}px` }"
            ></div>
            
            <!-- Vertical lines -->
            <div 
              v-for="i in days.length" 
              :key="`v-${i}`"
              class="grid-line vertical"
              :style="{ left: `${(i - 1) * (100 / days.length)}%` }"
            ></div>
          </div>

          <!-- Events -->
          <CalendarEvent
            v-for="event in events"
            :key="event.id"
            :event="event"
            :style="getEventStyle(event)"
          />
        </div>
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup>
import { ref, computed } from 'vue'

const timeSlots = ref([
  '09', '10', '11', '12', '13', '14', '15', '16'
])

const days = ref([
  { name: 'Monday', date: '12' },
  { name: 'Tuesday', date: '13' },
  { name: 'Wednesday', date: '14' },
  { name: 'Thursday', date: '15' },
  { name: 'Friday', date: '16' },
  { name: 'Saturday', date: '17' },
  { name: 'Sunday', date: '18' }
])

const events = ref([
  {
    id: 1,
    name: 'Shucho Deo',
    avatar: 'https://via.placeholder.com/24x24/4A90E2/FFFFFF?text=SD',
    color: '#6B73FF',
    day: 0, // Monday
    startTime: 10,
    duration: 2
  },
  {
    id: 2,
    name: 'Amelia',
    avatar: 'https://via.placeholder.com/24x24/FF6B6B/FFFFFF?text=A',
    color: '#FF9F43',
    day: 1, // Tuesday
    startTime: 11,
    duration: 1.5
  },
  {
    id: 3,
    name: 'Lija sarker',
    avatar: 'https://via.placeholder.com/24x24/26DE81/FFFFFF?text=LS',
    color: '#26DE81',
    day: 2, // Wednesday
    startTime: 9,
    duration: 3
  },
  {
    id: 4,
    name: 'Oliver',
    avatar: 'https://via.placeholder.com/24x24/FD79A8/FFFFFF?text=O',
    color: '#FD79A8',
    day: 3, // Thursday
    startTime: 12,
    duration: 2
  },
  {
    id: 5,
    name: 'Oliver',
    avatar: 'https://via.placeholder.com/24x24/FD79A8/FFFFFF?text=O',
    color: '#FD79A8',
    day: 4, // Friday
    startTime: 14,
    duration: 1
  },
  {
    id: 6,
    name: 'Anya Kabir',
    avatar: 'https://via.placeholder.com/24x24/A29BFE/FFFFFF?text=AK',
    color: '#A29BFE',
    day: 5, // Saturday
    startTime: 10,
    duration: 2.5
  },
  {
    id: 7,
    name: 'Anya Kabir',
    avatar: 'https://via.placeholder.com/24x24/A29BFE/FFFFFF?text=AK',
    color: '#A29BFE',
    day: 6, // Sunday
    startTime: 13,
    duration: 1.5
  }
])

const getEventStyle = (event) => {
  const dayWidth = 100 / days.value.length
  const hourHeight = 60 // pixels per hour
  
  const left = event.day * dayWidth
  const top = (event.startTime - 9) * hourHeight // 9 is the first hour
  const height = event.duration * hourHeight
  
  return {
    position: 'absolute',
    left: `${left}%`,
    top: `${top}px`,
    height: `${height}px`,
    width: `${dayWidth - 1}%`, // Small gap between days
    backgroundColor: event.color,
    zIndex: 10
  }
}
</script>

<style scoped>
.calendar-container {
  background: white;
  overflow: hidden;
  box-shadow: 0px 2px 5px 0px rgba(38, 51, 77, 0.03);
}

.calendar-header {
  display: grid;
  grid-template-columns: 80px repeat(7, 1fr);
  border-bottom: 1px solid #F5F5F5;
  background: white;
}

.time-header {
  padding: 16px;
  border-right: 1px solid #F5F5F5;
}

.day-header {
  padding: 16px 12px;
  text-align: center;
  border-right: 1px solid #F5F5F5;
}

.day-header:last-child {
  border-right: none;
}

.day-name {
  font-size: 13px;
  font-weight: 700;
  color: #4D5E80;
  margin-bottom: 4px;
}

.day-date {
  font-size: 13px;
  font-weight: 700;
  color: #C3C9D6;
}

.calendar-body {
  display: grid;
  grid-template-columns: 80px 1fr;
  position: relative;
  height: 480px; /* 8 hours * 60px */
}

.time-column {
  border-right: 1px solid #F5F5F5;
  background: white;
}

.time-slot {
  height: 60px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #F5F5F5;
}

.time-text {
  font-size: 13px;
  font-weight: 700;
  color: #4D5E80;
}

.time-icon {
  opacity: 0.6;
}

.calendar-grid {
  position: relative;
  background: white;
}

.grid-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  background-color: #F5F5F5;
}

.grid-line.horizontal {
  left: 0;
  right: 0;
  height: 1px;
}

.grid-line.vertical {
  top: 0;
  bottom: 0;
  width: 1px;
}
</style>
