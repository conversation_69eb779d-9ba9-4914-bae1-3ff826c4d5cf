<template>
  <v-card
    class="policy-section"
    elevation="0"
    rounded="lg"
  >
    <!-- Section Header -->
    <v-card-title class="section-header pa-6 pb-4">
      <v-row align="center" justify="space-between" no-gutters>
        <v-col cols="auto">
          <v-row align="center" no-gutters class="ga-5">
            <v-col cols="auto">
              <span class="text-h5 font-weight-bold text-grey-900">{{ title }}</span>
            </v-col>
            
            <!-- Toggle Switch (for Deductible Leaves) -->
            <v-col cols="auto" v-if="showToggle">
              <v-switch
                v-model="toggleState"
                color="primary"
                hide-details
                density="compact"
                class="toggle-switch"
              />
            </v-col>
            
            <!-- Info Icon -->
            <v-col cols="auto">
              <v-icon color="warning" size="24">mdi-alert-circle-outline</v-icon>
            </v-col>
          </v-row>
        </v-col>

        <v-col cols="auto">
          <v-row align="center" no-gutters class="ga-6">
            <!-- How do deductibles work? -->
            <v-col cols="auto">
              <v-btn
                variant="text"
                class="text-grey-600"
                prepend-icon="mdi-briefcase-alert-outline"
                size="small"
              >
                How do deductibles work?
              </v-btn>
            </v-col>

            <!-- Add New Button -->
            <v-col cols="auto">
              <v-btn
                color="primary"
               
                size="default"
                rounded="lg"
              >
                Add New
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card-title>

    <!-- Table Header -->
    <div class="table-header bg-grey-100 pa-0">
      <v-row no-gutters class="header-row">
        <v-col cols="3" class="header-cell pa-3">
          <span class="text-body-2 font-weight-medium text-grey-700">Name</span>
        </v-col>
        <v-col :cols="showAllowance ? 4 : 6" class="header-cell pa-3">
          <span class="text-body-2 font-weight-medium text-grey-700">Description</span>
        </v-col>
        <v-col :cols="showAllowance ? 3 : 3" class="header-cell pa-3">
          <span class="text-body-2 font-weight-medium text-grey-700">Requires Approval</span>
        </v-col>
        <v-col v-if="showAllowance" cols="2" class="header-cell pa-3">
          <span class="text-body-2 font-weight-medium text-grey-700">Allowance</span>
        </v-col>
      </v-row>
    </div>

    <!-- Table Content -->
    <v-card-text class="pa-0">
      <div v-for="(item, index) in items" :key="index" class="table-row">
        <v-row no-gutters class="data-row align-center">
          <!-- Name Column -->
          <v-col cols="3" class="data-cell pa-6">
            <v-row align="center" no-gutters class="ga-2">
              <v-col cols="auto">
                <span class="text-body-1 font-weight-medium text-grey-900">{{ item.name }}</span>
              </v-col>
              <v-col cols="auto">
                <v-chip
                  size="small"
                  color="grey-100"
                  text-color="grey-600"
                  class="default-chip"
                >
                  {{ item.label }}
                </v-chip>
              </v-col>
            </v-row>
          </v-col>

          <!-- Description Column -->
          <v-col :cols="showAllowance ? 4 : 6" class="data-cell pa-6">
            <span class="text-body-1 text-grey-700">{{ item.description }}</span>
          </v-col>

          <!-- Requires Approval Column -->
          <v-col :cols="showAllowance ? 3 : 3" class="data-cell pa-6">
            <span class="text-body-1 text-grey-700">{{ item.requiresApproval }}</span>
          </v-col>

          <!-- Allowance Column (only for deductible leaves) -->
          <v-col v-if="showAllowance" cols="2" class="data-cell pa-6 text-right">
            <span class="text-body-1 font-weight-medium text-grey-900">{{ item.allowance }}</span>
          </v-col>
        </v-row>

        <!-- Divider between rows -->
        <v-divider v-if="index < items.length - 1" class="mx-6" />
      </div>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
interface PolicyItem {
  name: string
  description: string
  requiresApproval: string
  allowance?: string
  label: string
}

interface Props {
  title: string
  showToggle?: boolean
  toggleValue?: boolean
  showAllowance?: boolean
  items: PolicyItem[]
  headers: Array<{ title: string; key: string }>
}

const props = withDefaults(defineProps<Props>(), {
  showToggle: false,
  toggleValue: false,
  showAllowance: false
})

const toggleState = ref(props.toggleValue)
</script>

<style scoped>
.policy-section {
  border: 1px solid #E5E5E5;
  background: white;
}

.section-header {
  border-bottom: none;
}

.table-header {
  border-top: 1px solid #E5E5E5;
}

.header-row {
  min-height: 42px;
}

.header-cell {
  display: flex;
  align-items: center;
}

.data-row {
  min-height: 60px;
}

.data-cell {
  display: flex;
  align-items: center;
}

.table-row:hover {
  background-color: #FAFAFA;
}

.default-chip {
  border-radius: 100px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-new-btn {
  padding: 11px 29px;
  font-weight: 500;
  font-size: 17px;
}

.toggle-switch :deep(.v-switch__track) {
  background-color: #FA4545;
}

.toggle-switch :deep(.v-switch__thumb) {
  background-color: white;
}
</style>
