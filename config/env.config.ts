// Environment-specific configuration
// This file handles environment variables and runtime configuration

export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test'
  NUXT_PUBLIC_APP_URL: string
  NUXT_PUBLIC_API_BASE_URL: string
  NUXT_PUBLIC_VAPID_KEY?: string
  NUXT_SLACK_WEBHOOK_URL?: string
  NUXT_TEAMS_WEBHOOK_URL?: string
  NUXT_MOCK_API?: string
  NUXT_LOG_LEVEL?: 'error' | 'warn' | 'info' | 'debug'
  NUXT_DATABASE_URL?: string
  NUXT_JWT_SECRET?: string
  NUXT_SMTP_HOST?: string
  NUXT_SMTP_PORT?: string
  NUXT_SMTP_USER?: string
  NUXT_SMTP_PASS?: string
  NUXT_REDIS_URL?: string
  NUXT_STORAGE_PROVIDER?: 'local' | 's3' | 'cloudinary'
  NUXT_S3_BUCKET?: string
  NUXT_S3_REGION?: string
  NUXT_S3_ACCESS_KEY?: string
  NUXT_S3_SECRET_KEY?: string
}

// Get environment variables with defaults
export const getEnvConfig = (): EnvironmentConfig => {
  const runtimeConfig = useRuntimeConfig()
  
  return {
    NODE_ENV: (process.env.NODE_ENV as any) || 'development',
    NUXT_PUBLIC_APP_URL: runtimeConfig.public.appUrl || process.env.NUXT_PUBLIC_APP_URL || 'http://localhost:3000',
    NUXT_PUBLIC_API_BASE_URL: runtimeConfig.public.apiBaseUrl || process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
    NUXT_PUBLIC_VAPID_KEY: runtimeConfig.public.vapidKey || process.env.NUXT_PUBLIC_VAPID_KEY,
    NUXT_SLACK_WEBHOOK_URL: runtimeConfig.slackWebhookUrl || process.env.NUXT_SLACK_WEBHOOK_URL,
    NUXT_TEAMS_WEBHOOK_URL: runtimeConfig.teamsWebhookUrl || process.env.NUXT_TEAMS_WEBHOOK_URL,
    NUXT_MOCK_API: runtimeConfig.mockApi || process.env.NUXT_MOCK_API,
    NUXT_LOG_LEVEL: (runtimeConfig.logLevel || process.env.NUXT_LOG_LEVEL) as any,
    NUXT_DATABASE_URL: runtimeConfig.databaseUrl || process.env.NUXT_DATABASE_URL,
    NUXT_JWT_SECRET: runtimeConfig.jwtSecret || process.env.NUXT_JWT_SECRET,
    NUXT_SMTP_HOST: runtimeConfig.smtpHost || process.env.NUXT_SMTP_HOST,
    NUXT_SMTP_PORT: runtimeConfig.smtpPort || process.env.NUXT_SMTP_PORT,
    NUXT_SMTP_USER: runtimeConfig.smtpUser || process.env.NUXT_SMTP_USER,
    NUXT_SMTP_PASS: runtimeConfig.smtpPass || process.env.NUXT_SMTP_PASS,
    NUXT_REDIS_URL: runtimeConfig.redisUrl || process.env.NUXT_REDIS_URL,
    NUXT_STORAGE_PROVIDER: (runtimeConfig.storageProvider || process.env.NUXT_STORAGE_PROVIDER) as any,
    NUXT_S3_BUCKET: runtimeConfig.s3Bucket || process.env.NUXT_S3_BUCKET,
    NUXT_S3_REGION: runtimeConfig.s3Region || process.env.NUXT_S3_REGION,
    NUXT_S3_ACCESS_KEY: runtimeConfig.s3AccessKey || process.env.NUXT_S3_ACCESS_KEY,
    NUXT_S3_SECRET_KEY: runtimeConfig.s3SecretKey || process.env.NUXT_S3_SECRET_KEY,
  }
}

// Development configuration overrides
export const developmentConfig = {
  api: {
    baseUrl: 'http://localhost:8000/api',
    timeout: 10000,
  },
  development: {
    debug: true,
    mockApi: true,
    showDevTools: true,
    logLevel: 'debug' as const,
  },
  security: {
    sessionTimeout: 60, // 1 hour for development
  },
}

// Production configuration overrides
export const productionConfig = {
  development: {
    debug: false,
    mockApi: false,
    showDevTools: false,
    logLevel: 'error' as const,
  },
  security: {
    sessionTimeout: 480, // 8 hours for production
  },
  ui: {
    animations: {
      enabled: true,
      duration: 200, // Faster animations in production
    },
  },
}

// Test configuration overrides
export const testConfig = {
  api: {
    baseUrl: 'http://localhost:8001/api',
    timeout: 5000,
  },
  development: {
    debug: false,
    mockApi: true,
    showDevTools: false,
    logLevel: 'warn' as const,
  },
  ui: {
    animations: {
      enabled: false, // Disable animations in tests
      duration: 0,
    },
  },
}

// Get environment-specific config
export const getEnvironmentSpecificConfig = () => {
  const env = process.env.NODE_ENV || 'development'
  
  switch (env) {
    case 'production':
      return productionConfig
    case 'test':
      return testConfig
    case 'development':
    default:
      return developmentConfig
  }
}

// Merge environment config with base config
export const mergeEnvironmentConfig = (baseConfig: any) => {
  const envConfig = getEnvironmentSpecificConfig()
  
  // Deep merge function
  const deepMerge = (target: any, source: any) => {
    const result = { ...target }
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(target[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    
    return result
  }
  
  return deepMerge(baseConfig, envConfig)
}

// Validate required environment variables
export const validateEnvironmentConfig = () => {
  const requiredVars = [
    'NUXT_PUBLIC_APP_URL',
    'NUXT_PUBLIC_API_BASE_URL',
  ]
  
  const missing = requiredVars.filter(varName => {
    const value = process.env[varName]
    return !value || value.trim() === ''
  })
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }
}

// Environment helpers
export const isProduction = () => process.env.NODE_ENV === 'production'
export const isDevelopment = () => process.env.NODE_ENV === 'development'
export const isTest = () => process.env.NODE_ENV === 'test'

// Feature flags based on environment
export const getFeatureFlags = () => {
  const env = process.env.NODE_ENV || 'development'
  
  return {
    enableAnalytics: env === 'production',
    enableErrorReporting: env === 'production',
    enablePerformanceMonitoring: env === 'production',
    enableDebugMode: env === 'development',
    enableMockData: env !== 'production',
    enableDevTools: env === 'development',
  }
}
