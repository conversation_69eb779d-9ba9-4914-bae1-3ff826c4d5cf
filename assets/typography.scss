// Global Typography Styles
// Based on TT <PERSON><PERSON> font family from Figma design

// Global font family application
* {
  font-family: 'TT Hoves', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// Override Vuetify's default font family
.v-application {
  font-family: 'TT Hoves', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
}

// Heading utility classes based on Figma design
.text-h1-website {
  font-family: 'TT Hoves', sans-serif;
  font-size: 100px;
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-h2-website {
  font-family: 'TT Hoves', sans-serif;
  font-size: 80px;
  font-weight: 600;
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.text-h3-website {
  font-family: 'TT <PERSON><PERSON>', sans-serif;
  font-size: 64px;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

.text-h4-website {
  font-family: 'TT Hoves', sans-serif;
  font-size: 48px;
  font-weight: 600;
  line-height: 1.2;
  letter-spacing: -0.01em;
}

.text-h5-website {
  font-family: 'TT Hoves', sans-serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 1.5;
}

// Dashboard heading classes
.text-h1-dashboard {
  font-family: 'TT Hoves', sans-serif;
  font-size: 48px;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-h2-dashboard {
  font-family: 'TT Hoves', sans-serif;
  font-size: 40px;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-h3-dashboard {
  font-family: 'TT Hoves', sans-serif;
  font-size: 32px;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-h4-dashboard {
  font-family: 'TT Hoves', sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

// Body text utility classes
.text-body-24-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.text-body-24-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 24px;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.text-body-24-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 24px;
  font-weight: 400;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

.text-body-20-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-body-20-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 20px;
  font-weight: 500;
  line-height: 1.5;
}

.text-body-20-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-body-18-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.text-body-18-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 18px;
  font-weight: 500;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.text-body-18-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 18px;
  font-weight: 400;
  line-height: 1.6;
}

.text-body-16-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.text-body-16-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.6;
  letter-spacing: -0.02em;
}

.text-body-16-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.6;
}

.text-body-14-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 14px;
  font-weight: 600;
  line-height: 1.6;
}

.text-body-14-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6;
}

.text-body-14-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

.text-body-12-bold {
  font-family: 'TT Hoves', sans-serif;
  font-size: 12px;
  font-weight: 600;
  line-height: 1.6;
}

.text-body-12-medium {
  font-family: 'TT Hoves', sans-serif;
  font-size: 12px;
  font-weight: 500;
  line-height: 1.6;
}

.text-body-12-regular {
  font-family: 'TT Hoves', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: 0.01em;
}

// Button text
.text-button-24 {
  font-family: 'TT Hoves', sans-serif;
  font-size: 24px;
  font-weight: 600;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

// Title classes
.text-title-1 {
  font-family: 'TT Hoves', sans-serif;
  font-size: 32px;
  font-weight: 500;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.text-title-2 {
  font-family: 'TT Hoves', sans-serif;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.5;
}

.text-title-3 {
  font-family: 'TT Hoves', sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.45;
  letter-spacing: -0.01em;
}

.text-caption {
  font-family: 'TT Hoves', sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 1.5;
  letter-spacing: -0.01em;
}

.text-caption-small {
  font-family: 'TT Hoves', sans-serif;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: -0.01em;
}

// Font weight utilities
.font-regular { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-demibold { font-weight: 600; }
.font-bold { font-weight: 700; }

// Responsive typography adjustments
@media (max-width: 768px) {
  .text-h1-website { font-size: 64px; }
  .text-h2-website { font-size: 48px; }
  .text-h3-website { font-size: 40px; }
  .text-h4-website { font-size: 32px; }
  .text-h5-website { font-size: 24px; }
  
  .text-h1-dashboard { font-size: 32px; }
  .text-h2-dashboard { font-size: 28px; }
  .text-h3-dashboard { font-size: 24px; }
  .text-h4-dashboard { font-size: 20px; }
}

@media (max-width: 480px) {
  .text-h1-website { font-size: 48px; }
  .text-h2-website { font-size: 40px; }
  .text-h3-website { font-size: 32px; }
  .text-h4-website { font-size: 28px; }
  .text-h5-website { font-size: 20px; }
  
  .text-h1-dashboard { font-size: 28px; }
  .text-h2-dashboard { font-size: 24px; }
  .text-h3-dashboard { font-size: 20px; }
  .text-h4-dashboard { font-size: 18px; }
}
