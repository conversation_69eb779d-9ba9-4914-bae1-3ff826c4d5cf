<script setup>
import { VNode<PERSON>enderer } from '@layouts/components/VNodeRenderer';
import { themeConfig } from '@themeConfig';

definePageMeta({
    layout: 'blank',
    public: true,

})

const form = ref({
    email: '',
    password: '',
    remember: false,
})

const selectedOption = ref('')

</script>

<template>
    <a href="javascript:void(0)">
        <div class="auth-logo d-flex align-center gap-x-3">
            <VNodeRenderer :nodes="themeConfig.app.logo" />
            <h1 class="auth-title">
                {{ themeConfig.app.title }}
            </h1>
        </div>
    </a>

    <VRow no-gutters class="auth-wrapper bg-surface">
        <VCol cols="12" md="5" sm="12" xs="12" class="auth-card-v2 d-flex align-center justify-center">
            <VCard flat :max-width="460" class="mt-12 mt-sm-0 pa-6">
                <!-- back button -->
                <div class="text-primary d-flex cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" xml:space="preserve" height="20px"
                        width="20px" class="me-1">
                        <path fill="none" stroke="#7367F0" stroke-width="2" stroke-miterlimit="10"
                            d="M6 16h22m-14 8.5L5.5 16 14 7.5" />
                    </svg>
                    <p>Back</p>
                </div>
                <!-- title -->
                <VCardText class="mb-4 pa-3">
                    <h3 class="text-h2 mb-1 font-weight-bold line-height-0">
                        Set up your leave policy
                    </h3>
                    <p class="mb-0 text-primary">
                        Step 3 of 3
                    </p>
                </VCardText>
                <VCardText class="pa-3">
                    <VForm @submit.prevent="() => { }">
                        <!-- leave types -->
                        <h6 class="text-h6 mb-2 font-weight-medium">Exam Leaves</h6>
                        <VRow class="mb-4">
                            <VCol cols="12" class="pe-2">
                                <VTextField v-model="form.timezone" :items="timeZones" label="Leaves type name"
                                    placeholder="Exam leaves" class="mb-2" />
                            </VCol>
                        </VRow>

                        <VRow>
                            <!-- option -->
                            <VRow>
                                <VCol cols="8" class="pe-2">
                                    <VRadioGroup v-model="selectedOption">
                                        <VRadio label="Credit yearly" value="one"
                                            :class="selectedOption === 'one' ? 'text-primary' : ''"></VRadio>
                                        <VRadio label="Credit monthly" value="two"
                                            :class="selectedOption === 'two' ? 'text-primary' : ''"></VRadio>
                                        <VRadio label="Unlimited leaves" value="three"
                                            :class="selectedOption === 'three' ? 'text-primary' : ''"></VRadio>
                                    </VRadioGroup>
                                </VCol>
                                <VCol cols="4" class="pe-6">
                                    <VTextField v-model="form.name" autofocus label="Every year" type="number"
                                        placeholder="20" class="text-primary" />
                                </VCol>
                            </VRow>

                            <VCol cols="12" class="">
                                <VBtn block type="submit" color="primary" class="my-6">
                                    Create leaves type
                                </VBtn>
                            </VCol>

                            <!-- have a doubt -->
                            <VCol cols="12"
                                class="text-body-1 text-center text-primary align-center justify-center items-center d-flex">
                                <span class="d-inline-block">
                                    Have a doubt?
                                </span>
                                <a class="text-primary ms-1 d-inline-block text-body-1 font-weight-medium align-center d-flex"
                                    href="javascript:void(0)">
                                    Chat with us
                                    <svg width="20" height="20" viewBox="0 0 15 15" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                            d="M3.646 11.354a.5.5 0 0 1 0-.707L10.293 4H6a.5.5 0 0 1 0-1h5.5a.5.5 0 0 1 .5.5V9a.5.5 0 0 1-1 0V4.707l-6.646 6.647a.5.5 0 0 1-.708 0"
                                            fill="#7367F0" />
                                    </svg>
                                </a>
                            </VCol>
                        </VRow>
                    </VForm>
                </VCardText>
            </VCard>
        </VCol>

        <VCol cols="12" md="7" sm="0" class="d-none d-md-flex">
            <div class="position-relative bg-background w-100 me-0">
                <div class="d-flex align-center justify-center w-100 h-100" style="padding-inline: 6.25rem;">
                    <!-- <VImg
                max-width="613"
                :src="authThemeImg"
                class="auth-illustration mt-16 mb-2"
              /> -->
                </div>

                <!-- <img
              class="auth-footer-mask flip-in-rtl"
              :src="authThemeMask"
              alt="auth-footer-mask"
              height="280"
              width="100"
            > -->
            </div>
        </VCol>
    </VRow>
</template>

<style lang="scss">
@use '@/assets/pages/page-auth.scss';
</style>
