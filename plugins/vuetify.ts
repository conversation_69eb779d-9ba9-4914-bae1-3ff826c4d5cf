// plugins/vuetify.ts
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'

export default defineNuxtPlugin((app) => {
  const vuetify = createVuetify({
    components,
    directives,
    theme: {
      defaultTheme: 'light',
      themes: {
        light: {
          dark: false,
          colors: {
            // Primary brand colors from Figma design
            primary: '#FA4545',        // Red - main brand color
            'primary-darken-1': '#E53E3E',
            'primary-lighten-1': '#FC8181',

            // Secondary colors
            secondary: '#48A9A6',      // Teal/cyan color
            'secondary-darken-1': '#3a8785',
            'secondary-lighten-1': '#6bb8b5',

            // Accent colors
            accent: '#EBF3FF',         // Light blue accent
            'accent-darken-1': '#d6e8ff',

            // Success colors (green tones)
            success: '#CEEFDF',        // Light green
            'success-darken-1': '#4ade80',
            'success-lighten-1': '#dcfce7',

            // Warning colors (orange/yellow tones)
            warning: '#FEEDDA',        // Light orange/peach
            'warning-darken-1': '#f59e0b',
            'warning-lighten-1': '#fef3c7',

            // Error colors
            error: '#ef4444',
            'error-darken-1': '#dc2626',
            'error-lighten-1': '#fecaca',

            // Info colors
            info: '#3b82f6',
            'info-darken-1': '#2563eb',
            'info-lighten-1': '#dbeafe',

            // Surface and background colors
            surface: '#FFFFFF',
            'surface-bright': '#FFFFFF',
            'surface-light': '#FAFAFA',   // Very light gray
            'surface-variant': '#F4F4F5', // Light gray variant
            background: '#FFFFFF',

            // Text colors
            'on-surface': '#1A202C',      // Dark text on light surfaces
            'on-primary': '#FFFFFF',      // White text on primary
            'on-secondary': '#FFFFFF',    // White text on secondary
            'on-success': '#166534',      // Dark green text on success
            'on-warning': '#92400e',      // Dark orange text on warning
            'on-error': '#FFFFFF',        // White text on error
            'on-info': '#FFFFFF',         // White text on info
            'on-background': '#1A202C',   // Dark text on background

            // Additional utility colors
            'grey-50': '#FAFAFA',
            'grey-100': '#F4F4F5',
            'grey-200': '#E4E4E7',
            'grey-300': '#D4D4D8',
            'grey-400': '#A1A1AA',
            'grey-500': '#71717A',
            'grey-600': '#52525B',
            'grey-700': '#3F3F46',
            'grey-800': '#27272A',
            'grey-900': '#18181B',
          }
        },
        dark: {
          dark: true,
          colors: {
            // Primary brand colors (adjusted for dark theme)
            primary: '#FC8181',        // Lighter red for dark mode
            'primary-darken-1': '#FA4545',
            'primary-lighten-1': '#FEB2B2',

            // Secondary colors
            secondary: '#06B6D4',      // Brighter teal for dark mode
            'secondary-darken-1': '#0891B2',
            'secondary-lighten-1': '#22D3EE',

            // Accent colors
            accent: '#1E293B',         // Dark blue accent
            'accent-darken-1': '#0F172A',

            // Success colors
            success: '#10B981',
            'success-darken-1': '#059669',
            'success-lighten-1': '#34D399',

            // Warning colors
            warning: '#F59E0B',
            'warning-darken-1': '#D97706',
            'warning-lighten-1': '#FBBF24',

            // Error colors
            error: '#EF4444',
            'error-darken-1': '#DC2626',
            'error-lighten-1': '#F87171',

            // Info colors
            info: '#3B82F6',
            'info-darken-1': '#2563EB',
            'info-lighten-1': '#60A5FA',

            // Surface and background colors for dark theme
            surface: '#1E293B',
            'surface-bright': '#334155',
            'surface-light': '#475569',
            'surface-variant': '#64748B',
            background: '#0F172A',

            // Text colors for dark theme
            'on-surface': '#F1F5F9',
            'on-primary': '#FFFFFF',
            'on-secondary': '#FFFFFF',
            'on-success': '#FFFFFF',
            'on-warning': '#000000',
            'on-error': '#FFFFFF',
            'on-info': '#FFFFFF',
            'on-background': '#F1F5F9',
          }
        }
      }
    },
    defaults: {
      VBtn: {
        style: 'font-family: "TT Hoves", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;'
      },
      VCard: {
        style: 'font-family: "TT Hoves", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;'
      },
      VAppBar: {
        style: 'font-family: "TT Hoves", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;'
      }
    }
  })
  app.vueApp.use(vuetify)
})
