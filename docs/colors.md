# Color System Documentation

This document outlines the color system implemented for the Leave Management System based on the Figma design.

## Primary Colors

### Primary Brand Color
- **Primary**: `#FA4545` - Red, main brand color
- **Primary Darken 1**: `#E53E3E` - Darker red variant
- **Primary Lighten 1**: `#FC8181` - Lighter red variant

**Usage**: Main navigation, primary buttons, headings, brand elements

### Secondary Color
- **Secondary**: `#48A9A6` - Teal/cyan color
- **Secondary Darken 1**: `#3a8785` - Darker variant
- **Secondary Lighten 1**: `#6bb8b5` - Lighter variant

**Usage**: Secondary buttons, accent elements, highlights

## Semantic Colors

### Success Colors
- **Success**: `#CEEFDF` - Light green background
- **Success Darken 1**: `#4ade80` - Green for text/icons
- **Success Lighten 1**: `#dcfce7` - Very light green

**Usage**: Success messages, approved status, positive indicators

### Warning Colors
- **Warning**: `#FEEDDA` - Light orange/peach background
- **Warning Darken 1**: `#f59e0b` - Orange for text/icons
- **Warning Lighten 1**: `#fef3c7` - Very light orange

**Usage**: Warning messages, pending status, caution indicators

### Error Colors
- **Error**: `#ef4444` - Red
- **Error Darken 1**: `#dc2626` - Darker red
- **Error Lighten 1**: `#fecaca` - Light red

**Usage**: Error messages, rejected status, danger indicators

### Info Colors
- **Info**: `#3b82f6` - Blue
- **Info Darken 1**: `#2563eb` - Darker blue
- **Info Lighten 1**: `#dbeafe` - Light blue

**Usage**: Information messages, neutral status, help text

## Surface Colors

### Light Theme
- **Surface**: `#FFFFFF` - White
- **Surface Bright**: `#FFFFFF` - Bright white
- **Surface Light**: `#FAFAFA` - Very light gray
- **Surface Variant**: `#F4F4F5` - Light gray variant
- **Background**: `#FFFFFF` - White background

### Accent Surface
- **Accent**: `#EBF3FF` - Light blue accent background
- **Accent Darken 1**: `#d6e8ff` - Darker accent

## Text Colors

### On Light Surfaces
- **On Surface**: `#1A202C` - Dark text
- **On Background**: `#1A202C` - Dark text
- **On Primary**: `#FFFFFF` - White text on primary
- **On Secondary**: `#FFFFFF` - White text on secondary
- **On Success**: `#166534` - Dark green text on success
- **On Warning**: `#92400e` - Dark orange text on warning
- **On Error**: `#FFFFFF` - White text on error
- **On Info**: `#FFFFFF` - White text on info

## Gray Scale

- **Grey 50**: `#FAFAFA` - Lightest gray
- **Grey 100**: `#F4F4F5` - Very light gray
- **Grey 200**: `#E4E4E7` - Light gray
- **Grey 300**: `#D4D4D8` - Medium light gray
- **Grey 400**: `#A1A1AA` - Medium gray
- **Grey 500**: `#71717A` - Medium gray
- **Grey 600**: `#52525B` - Medium dark gray
- **Grey 700**: `#3F3F46` - Dark gray
- **Grey 800**: `#27272A` - Very dark gray
- **Grey 900**: `#18181B` - Darkest gray

## Usage Examples

### In Vue Components
```vue
<template>
  <!-- Primary button -->
  <v-btn color="primary">Submit Request</v-btn>
  
  <!-- Secondary button -->
  <v-btn color="secondary" variant="outlined">Cancel</v-btn>
  
  <!-- Success alert -->
  <v-alert type="success">Request approved!</v-alert>
  
  <!-- Warning card -->
  <v-card color="warning">
    <v-card-text class="text-warning-darken-1">
      Pending approval
    </v-card-text>
  </v-card>
  
  <!-- Custom colored text -->
  <p class="text-primary">Important information</p>
  <p class="text-secondary">Secondary information</p>
</template>
```

### CSS Classes
```css
/* Background colors */
.bg-primary { background-color: #FA4545; }
.bg-secondary { background-color: #48A9A6; }
.bg-success { background-color: #CEEFDF; }
.bg-warning { background-color: #FEEDDA; }

/* Text colors */
.text-primary { color: #FA4545; }
.text-secondary { color: #48A9A6; }
.text-success { color: #4ade80; }
.text-warning { color: #f59e0b; }
```

## Dark Theme Support

The color system includes a dark theme variant with adjusted colors for better contrast and readability in dark environments. The dark theme automatically adjusts:

- Primary colors become brighter for better visibility
- Surface colors use dark backgrounds
- Text colors are inverted for proper contrast
- Semantic colors maintain their meaning while being dark-theme appropriate

## Accessibility

All color combinations have been chosen to meet WCAG 2.1 AA contrast requirements:
- Primary text on white backgrounds: 21:1 contrast ratio
- Secondary colors maintain at least 4.5:1 contrast
- Interactive elements have clear focus states
- Color is never the only way to convey information
