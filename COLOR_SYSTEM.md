# 🎨 Leave Management System - Color System

## Overview
The color system has been implemented based on the Figma design with `#FA4545` as the primary brand color. This creates a modern, professional look suitable for a leave management application.

## 🔴 Primary Colors

### Main Brand Color
- **Primary**: `#FA4545` (Red)
- **Primary Darken 1**: `#E53E3E` (Darker Red)
- **Primary Lighten 1**: `#FC8181` (Lighter Red)

**Usage**: Navigation bars, primary buttons, headings, brand elements

## 🌊 Secondary Colors

### Accent Color
- **Secondary**: `#48A9A6` (<PERSON><PERSON>/<PERSON>an)
- **Secondary Darken 1**: `#3a8785`
- **Secondary Lighten 1**: `#6bb8b5`

**Usage**: Secondary buttons, accent elements, highlights, complementary actions

## 🎯 Semantic Colors

### ✅ Success (Green)
- **Success**: `#CEEFDF` (Light Green Background)
- **Success Darken 1**: `#4ade80` (Green Text/Icons)
- **Success Lighten 1**: `#dcfce7` (Very Light Green)

### ⚠️ Warning (Orange)
- **Warning**: `#FEEDDA` (Light Orange Background)
- **Warning Darken 1**: `#f59e0b` (Orange Text/Icons)
- **Warning Lighten 1**: `#fef3c7` (Very Light Orange)

### ❌ Error (Red)
- **Error**: `#ef4444` (Red)
- **Error Darken 1**: `#dc2626` (Darker Red)
- **Error Lighten 1**: `#fecaca` (Light Red)

### ℹ️ Info (Blue)
- **Info**: `#3b82f6` (Blue)
- **Info Darken 1**: `#2563eb` (Darker Blue)
- **Info Lighten 1**: `#dbeafe` (Light Blue)

## 🏗️ Surface Colors

### Light Theme
- **Surface**: `#FFFFFF` (White)
- **Surface Light**: `#FAFAFA` (Very Light Gray)
- **Surface Variant**: `#F4F4F5` (Light Gray)
- **Background**: `#FFFFFF` (White)
- **Accent**: `#EBF3FF` (Light Blue Accent)

## 📝 Text Colors

- **On Surface**: `#1A202C` (Dark Text)
- **On Primary**: `#FFFFFF` (White on Red)
- **On Secondary**: `#FFFFFF` (White on Teal)
- **On Success**: `#166534` (Dark Green)
- **On Warning**: `#92400e` (Dark Orange)
- **On Error**: `#FFFFFF` (White on Red)
- **On Info**: `#FFFFFF` (White on Blue)

## 🌙 Dark Theme Support

The system includes a complete dark theme with:
- **Primary**: `#FC8181` (Lighter red for better visibility)
- **Background**: `#0F172A` (Dark navy)
- **Surface**: `#1E293B` (Dark gray)
- All semantic colors adjusted for dark backgrounds

## 🚀 Quick Usage Examples

### Vue Components
```vue
<!-- Primary Actions -->
<v-btn color="primary">Submit Leave Request</v-btn>
<v-app-bar color="primary">Navigation</v-app-bar>

<!-- Secondary Actions -->
<v-btn color="secondary" variant="outlined">View Details</v-btn>

<!-- Status Indicators -->
<v-alert type="success">Leave approved!</v-alert>
<v-alert type="warning">Pending approval</v-alert>
<v-alert type="error">Request denied</v-alert>
<v-alert type="info">Remember to submit early</v-alert>

<!-- Custom Colored Elements -->
<v-card color="accent">
  <v-card-text class="text-primary">
    Important information
  </v-card-text>
</v-card>
```

### CSS Classes
```css
/* Background Colors */
.bg-primary { background-color: #FA4545; }
.bg-secondary { background-color: #48A9A6; }

/* Text Colors */
.text-primary { color: #FA4545; }
.text-secondary { color: #48A9A6; }
```

## 🎨 Color Palette Preview

The application now displays a comprehensive color palette showcase including:
- Primary and secondary color cards
- Success and warning color examples
- Status indicators with proper semantic colors
- Feature cards demonstrating color usage
- Alert components showing all semantic states

## ✅ Implementation Status

- ✅ Primary color updated to `#FA4545`
- ✅ Complete color system implemented
- ✅ Light and dark theme support
- ✅ Semantic colors for all states
- ✅ Proper contrast ratios for accessibility
- ✅ Documentation and examples provided
- ✅ Live preview in application

## 🔧 Files Modified

1. `plugins/vuetify.ts` - Main theme configuration
2. `app.vue` - Color showcase and examples
3. `docs/colors.md` - Detailed color documentation
4. `COLOR_SYSTEM.md` - This overview file

The color system is now fully implemented and ready for use in your Leave Management System! 🎉
