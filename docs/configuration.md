# Configuration System Documentation

This document explains how to use the centralized configuration system for the Leave Management System.

## Overview

The configuration system provides a centralized way to manage all application settings, including:
- App metadata and branding
- Theme colors and typography
- API endpoints and settings
- Feature flags
- UI preferences
- Security settings
- Leave management policies
- Notification settings
- Integration configurations
- Development settings

## File Structure

```
config/
├── app.config.ts          # Main configuration file
└── env.config.ts           # Environment-specific configuration

composables/
└── useLeaveConfig.ts       # Configuration composable

examples/
└── config-usage.vue       # Usage examples
```

## Basic Usage

### 1. Using the Composable

```vue
<script setup>
// Import the configuration composable
const { app, theme, api, features } = useLeaveConfig();

// Access configuration values
console.log(app.title); // "Leave Management System"
console.log(theme.colors.primary); // "#FA4545"
console.log(api.baseUrl); // "http://localhost:8000/api"
</script>

<template>
  <div>
    <h1>{{ app.title }}</h1>
    <v-icon :color="theme.colors.primary">{{ app.logo.icon }}</v-icon>
  </div>
</template>
```

### 2. Using Helper Functions

```vue
<script setup>
const {
  getApiUrl,
  getThemeColor,
  getFontFamily,
  isFeatureEnabled,
  getLeaveTypeById
} = useLeaveConfig();

// Get full API URL
const loginUrl = getApiUrl('/auth/login');

// Get theme colors
const primaryColor = getThemeColor('primary');

// Check if feature is enabled
const hasAnalytics = isFeatureEnabled('analytics');

// Get leave type configuration
const sickLeave = getLeaveTypeById('sick');
</script>
```

## Configuration Sections

### App Configuration

```typescript
app: {
  name: 'leave-management-system',
  title: 'Leave Management System',
  description: 'Modern leave management system',
  version: '1.0.0',
  logo: {
    icon: 'mdi-calendar-check',
    text: 'Leave Management System',
    url: '/',
  },
  url: 'http://localhost:3000',
  author: {
    name: 'Your Company',
    email: '<EMAIL>',
  },
}
```

### Theme Configuration

```typescript
theme: {
  colors: {
    primary: '#FA4545',
    secondary: '#48A9A6',
    // ... more colors
  },
  typography: {
    fontFamily: {
      primary: "'TT Hoves', sans-serif",
      secondary: "system fonts",
    },
    sizes: {
      'h1-dashboard': '48px',
      'body-medium': '16px',
      // ... more sizes
    },
  },
  darkMode: true,
  defaultTheme: 'light',
}
```

### API Configuration

```typescript
api: {
  baseUrl: 'http://localhost:8000/api',
  timeout: 30000,
  retries: 3,
  endpoints: {
    auth: {
      login: '/auth/login',
      logout: '/auth/logout',
      // ... more endpoints
    },
    leave: {
      requests: '/leave/requests',
      types: '/leave/types',
      // ... more endpoints
    },
  },
}
```

### Feature Flags

```typescript
features: {
  multiTenant: false,
  notifications: {
    email: true,
    push: true,
    sms: false,
  },
  integrations: {
    calendar: true,
    slack: true,
    teams: false,
  },
  analytics: true,
  reporting: true,
}
```

### Leave Configuration

```typescript
leave: {
  types: [
    {
      id: 'annual',
      name: 'Annual Leave',
      color: '#4CAF50',
      icon: 'mdi-beach',
      maxDays: 25,
      carryOver: true,
    },
    // ... more leave types
  ],
  policies: {
    maxAdvanceBooking: 365,
    minNotice: 1,
    requireApproval: true,
  },
  workingDays: [1, 2, 3, 4, 5], // Monday to Friday
}
```

## Environment Configuration

### Environment Variables

Create a `.env` file in your project root:

```env
# App Configuration
NUXT_PUBLIC_APP_URL=http://localhost:3000
NUXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# Database
NUXT_DATABASE_URL=postgresql://user:pass@localhost:5432/leave_db

# Email
NUXT_SMTP_HOST=smtp.gmail.com
NUXT_SMTP_PORT=587
NUXT_SMTP_USER=<EMAIL>
NUXT_SMTP_PASS=your-app-password

# Integrations
NUXT_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/...
NUXT_PUBLIC_VAPID_KEY=your-vapid-key

# Development
NUXT_MOCK_API=true
NUXT_LOG_LEVEL=debug
```

### Environment-Specific Overrides

The system automatically applies environment-specific configurations:

- **Development**: Debug mode, mock API, detailed logging
- **Production**: Optimized settings, error logging only
- **Test**: Fast settings, animations disabled

## Advanced Usage

### 1. Runtime Configuration Updates

```vue
<script setup>
const { updateConfig } = useLeaveConfig();

// Update configuration at runtime
const toggleDarkMode = () => {
  updateConfig({
    theme: {
      defaultTheme: 'dark'
    }
  });
};
</script>
```

### 2. Feature Flag Checks

```vue
<script setup>
const { isFeatureEnabled } = useLeaveConfig();

// Conditionally render based on features
const showAnalytics = isFeatureEnabled('analytics');
const showSlackIntegration = isFeatureEnabled('integrations.slack');
</script>

<template>
  <div>
    <analytics-dashboard v-if="showAnalytics" />
    <slack-notifications v-if="showSlackIntegration" />
  </div>
</template>
```

### 3. API URL Generation

```vue
<script setup>
const { getApiUrl } = useLeaveConfig();

// Generate API URLs
const fetchUsers = async () => {
  const response = await $fetch(getApiUrl('/users'));
  return response;
};
</script>
```

### 4. Theme Integration

```vue
<script setup>
const { getThemeColor, getFontFamily } = useAppConfig();
</script>

<template>
  <div 
    :style="{
      color: getThemeColor('primary'),
      fontFamily: getFontFamily('primary')
    }"
  >
    Styled with theme configuration
  </div>
</template>
```

## Best Practices

### 1. Use Composable in Components

Always use the `useAppConfig()` composable instead of importing config directly:

```vue
<!-- ✅ Good -->
<script setup>
const { app } = useAppConfig();
</script>

<!-- ❌ Avoid -->
<script setup>
import { appConfig } from '~/config/app.config';
</script>
```

### 2. Environment Variables for Secrets

Never store sensitive data in the config files. Use environment variables:

```typescript
// ✅ Good
api: {
  baseUrl: process.env.NUXT_PUBLIC_API_BASE_URL,
}

// ❌ Avoid
api: {
  baseUrl: 'https://api.myapp.com',
  apiKey: 'secret-key-123', // Never do this!
}
```

### 3. Type Safety

The configuration system is fully typed. Use TypeScript for better development experience:

```typescript
import type { AppConfig } from '~/config/app.config';

const customConfig: Partial<AppConfig> = {
  theme: {
    colors: {
      primary: '#FF0000', // TypeScript will validate this
    }
  }
};
```

### 4. Feature Flags for Gradual Rollouts

Use feature flags to control feature availability:

```vue
<script setup>
const { isFeatureEnabled } = useLeaveConfig();

// Only show new feature if enabled
const showNewFeature = isFeatureEnabled('newFeature');
</script>
```

## Configuration Reference

See `examples/config-usage.vue` for a complete example of how to use all configuration options.

The configuration system provides a robust, type-safe way to manage all application settings while supporting environment-specific overrides and runtime updates.
