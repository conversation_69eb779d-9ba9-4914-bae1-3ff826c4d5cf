<template>
  <v-app>
    <v-main>
      <v-container class="fill-height" fluid>
        <v-row align="center" justify="center">
          <v-col cols="12" sm="8" md="6">
            <v-card class="elevation-12">
              <v-card-title class="text-h4 text-center pa-6">
                Leave Management System
              </v-card-title>
              <v-card-text class="text-center">
                <p class="text-h6 mb-6">Welcome to the Leave Management System</p>
                <div class="d-flex gap-4 justify-center">
                  <v-btn
                    color="primary"
                    size="large"
                    to="/leave-policy"
                    class="ma-2"
                  >
                    View Leave Policy
                  </v-btn>

                  <v-btn
                    color="secondary"
                    size="large"
                    to="/calendar"
                    class="ma-2"
                  >
                    View Calendar
                  </v-btn>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>
  </v-app>
</template>

<script setup>
// Set page title
useHead({
  title: 'Leave Management System'
})
</script>
