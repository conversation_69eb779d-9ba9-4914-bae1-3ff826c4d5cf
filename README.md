# Nuxt 3 + Vuetify 3 Setup

A modern web application built with Nuxt 3 and Vuetify 3, featuring Material Design components and theming.

## Features

- ⚡ **Nuxt 3** - The Intuitive Vue Framework
- 🎨 **Vuetify 3** - Material Design Component Framework
- 🎯 **Material Design Icons** - Beautiful iconography
- 🔥 **Hot Module Replacement** - Fast development experience
- 📱 **Responsive Design** - Mobile-first approach
- 🌙 **Theme Support** - Light/Dark mode ready

## Setup

Make sure to install dependencies:

```bash
# npm
npm install

# pnpm
pnpm install

# yarn
yarn install

# bun
bun install
```

## Development Server

Start the development server on `http://localhost:3000`:

```bash
# npm
npm run dev

# pnpm
pnpm dev

# yarn
yarn dev

# bun
bun run dev
```

## Production

Build the application for production:

```bash
# npm
npm run build

# pnpm
pnpm build

# yarn
yarn build

# bun
bun run build
```

Locally preview production build:

```bash
# npm
npm run preview

# pnpm
pnpm preview

# yarn
yarn preview

# bun
bun run preview
```

## Project Structure

```
├── assets/
│   └── main.scss          # Global styles and Vuetify imports
├── plugins/
│   └── vuetify.ts          # Vuetify plugin configuration
├── app.vue                 # Main application component
├── nuxt.config.ts          # Nuxt configuration with Vuetify setup
└── package.json            # Dependencies and scripts
```

## Dependencies

### Core Dependencies
- `nuxt` - The Nuxt 3 framework
- `vue` - Vue 3 framework
- `vuetify` - Material Design component framework
- `@mdi/font` - Material Design Icons

### Development Dependencies
- `vite-plugin-vuetify` - Vite plugin for Vuetify integration
- `sass` - SCSS preprocessor

## Configuration

The project is configured with:
- **Vuetify 3** with auto-import enabled
- **Material Design Icons** for iconography
- **SCSS** support for styling
- **Tree-shaking** for optimal bundle size

Check out the [Nuxt 3 documentation](https://nuxt.com/docs/getting-started/introduction) and [Vuetify 3 documentation](https://vuetifyjs.com/en/) for more information.
