// App Configuration
// Centralized configuration for the Leave Management System

export interface AppConfig {
  app: {
    name: string;
    title: string;
    description: string;
    version: string;
    logo: {
      icon: string;
      text: string;
      url?: string;
    };
    url: string;
    author: {
      name: string;
      email: string;
      website?: string;
    };
  };
  theme: {
    colors: {
      primary: string;
      'primary-darken-1': string;
      'primary-lighten-1': string;
      secondary: string;
      'secondary-darken-1': string;
      'secondary-lighten-1': string;
      accent: string;
      success: string;
      warning: string;
      error: string;
      info: string;
      background: string;
      surface: string;
      'surface-light': string;
      'surface-variant': string;
    };
    typography: {
      fontFamily: {
        primary: string;
        secondary: string;
      };
      sizes: {
        'h1-website': string;
        'h2-website': string;
        'h3-website': string;
        'h4-website': string;
        'h5-website': string;
        'h1-dashboard': string;
        'h2-dashboard': string;
        'h3-dashboard': string;
        'h4-dashboard': string;
        'body-large': string;
        'body-medium': string;
        'body-small': string;
        caption: string;
      };
    };
    darkMode: boolean;
    defaultTheme: 'light' | 'dark';
  };
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
    endpoints: {
      auth: {
        login: string;
        logout: string;
        register: string;
        refresh: string;
        profile: string;
      };
      leave: {
        requests: string;
        types: string;
        balance: string;
        calendar: string;
      };
      users: {
        list: string;
        profile: string;
        settings: string;
      };
      organization: {
        settings: string;
        departments: string;
        holidays: string;
      };
    };
  };
  features: {
    multiTenant: boolean;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
    integrations: {
      calendar: boolean;
      slack: boolean;
      teams: boolean;
    };
    analytics: boolean;
    reporting: boolean;
    approvalWorkflow: boolean;
    bulkOperations: boolean;
  };
  ui: {
    layout: {
      sidebar: {
        width: number;
        collapsedWidth: number;
        defaultCollapsed: boolean;
      };
      header: {
        height: number;
        fixed: boolean;
      };
      footer: {
        height: number;
        show: boolean;
      };
    };
    animations: {
      enabled: boolean;
      duration: number;
    };
    pagination: {
      defaultPageSize: number;
      pageSizeOptions: number[];
    };
    dateFormat: string;
    timeFormat: string;
    currency: {
      code: string;
      symbol: string;
      position: 'before' | 'after';
    };
  };
  security: {
    sessionTimeout: number; // in minutes
    passwordPolicy: {
      minLength: number;
      requireUppercase: boolean;
      requireLowercase: boolean;
      requireNumbers: boolean;
      requireSpecialChars: boolean;
    };
    twoFactorAuth: {
      enabled: boolean;
      methods: ('email' | 'sms' | 'app')[];
    };
  };
  leave: {
    types: {
      id: string;
      name: string;
      color: string;
      icon: string;
      maxDays?: number;
      carryOver?: boolean;
    }[];
    policies: {
      maxAdvanceBooking: number; // days
      minNotice: number; // days
      maxConsecutiveDays: number;
      requireApproval: boolean;
      autoApprovalLimit: number; // days
    };
    workingDays: number[]; // 0-6 (Sunday-Saturday)
    fiscalYearStart: string; // MM-DD format
  };
  notifications: {
    channels: {
      email: {
        enabled: boolean;
        templates: Record<string, string>;
      };
      push: {
        enabled: boolean;
        vapidKey?: string;
      };
      sms: {
        enabled: boolean;
        provider?: string;
      };
    };
    events: {
      leaveRequested: boolean;
      leaveApproved: boolean;
      leaveRejected: boolean;
      leaveCancelled: boolean;
      reminderDue: boolean;
      balanceUpdated: boolean;
    };
  };
  integrations: {
    calendar: {
      provider: 'google' | 'outlook' | 'apple' | null;
      syncEnabled: boolean;
    };
    slack: {
      enabled: boolean;
      webhookUrl?: string;
      channels: string[];
    };
    teams: {
      enabled: boolean;
      webhookUrl?: string;
    };
  };
  development: {
    debug: boolean;
    mockApi: boolean;
    showDevTools: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
  };
}

export const appConfig: AppConfig = {
  app: {
    name: 'leave-management-system',
    title: 'Leave Management System',
    description: 'Modern leave management system built with Nuxt 3 and Vuetify',
    version: '1.0.0',
    logo: {
      icon: 'mdi-calendar-check',
      text: 'Leave Management System',
      url: '/',
    },
    url: process.env.NUXT_PUBLIC_APP_URL || 'http://localhost:3000',
    author: {
      name: 'Your Company',
      email: '<EMAIL>',
      website: 'https://yourcompany.com',
    },
  },
  theme: {
    colors: {
      primary: '#FA4545',
      'primary-darken-1': '#E53E3E',
      'primary-lighten-1': '#FC8181',
      secondary: '#48A9A6',
      'secondary-darken-1': '#3a8785',
      'secondary-lighten-1': '#6bb8b5',
      accent: '#EBF3FF',
      success: '#CEEFDF',
      warning: '#FEEDDA',
      error: '#ef4444',
      info: '#3b82f6',
      background: '#FFFFFF',
      surface: '#FFFFFF',
      'surface-light': '#FAFAFA',
      'surface-variant': '#F4F4F5',
    },
    typography: {
      fontFamily: {
        primary: "'TT Hoves', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
        secondary: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif",
      },
      sizes: {
        'h1-website': '100px',
        'h2-website': '80px',
        'h3-website': '64px',
        'h4-website': '48px',
        'h5-website': '32px',
        'h1-dashboard': '48px',
        'h2-dashboard': '40px',
        'h3-dashboard': '32px',
        'h4-dashboard': '24px',
        'body-large': '18px',
        'body-medium': '16px',
        'body-small': '14px',
        caption: '12px',
      },
    },
    darkMode: true,
    defaultTheme: 'light',
  },
  api: {
    baseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
    timeout: 30000,
    retries: 3,
    endpoints: {
      auth: {
        login: '/auth/login',
        logout: '/auth/logout',
        register: '/auth/register',
        refresh: '/auth/refresh',
        profile: '/auth/profile',
      },
      leave: {
        requests: '/leave/requests',
        types: '/leave/types',
        balance: '/leave/balance',
        calendar: '/leave/calendar',
      },
      users: {
        list: '/users',
        profile: '/users/profile',
        settings: '/users/settings',
      },
      organization: {
        settings: '/organization/settings',
        departments: '/organization/departments',
        holidays: '/organization/holidays',
      },
    },
  },
  features: {
    multiTenant: false,
    notifications: {
      email: true,
      push: true,
      sms: false,
    },
    integrations: {
      calendar: true,
      slack: true,
      teams: false,
    },
    analytics: true,
    reporting: true,
    approvalWorkflow: true,
    bulkOperations: true,
  },
  ui: {
    layout: {
      sidebar: {
        width: 280,
        collapsedWidth: 80,
        defaultCollapsed: false,
      },
      header: {
        height: 64,
        fixed: true,
      },
      footer: {
        height: 60,
        show: true,
      },
    },
    animations: {
      enabled: true,
      duration: 300,
    },
    pagination: {
      defaultPageSize: 10,
      pageSizeOptions: [5, 10, 25, 50, 100],
    },
    dateFormat: 'YYYY-MM-DD',
    timeFormat: 'HH:mm',
    currency: {
      code: 'USD',
      symbol: '$',
      position: 'before',
    },
  },
  security: {
    sessionTimeout: 480, // 8 hours
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecialChars: true,
    },
    twoFactorAuth: {
      enabled: false,
      methods: ['email', 'app'],
    },
  },
  leave: {
    types: [
      {
        id: 'annual',
        name: 'Annual Leave',
        color: '#4CAF50',
        icon: 'mdi-beach',
        maxDays: 25,
        carryOver: true,
      },
      {
        id: 'sick',
        name: 'Sick Leave',
        color: '#FF9800',
        icon: 'mdi-medical-bag',
        maxDays: 10,
        carryOver: false,
      },
      {
        id: 'personal',
        name: 'Personal Leave',
        color: '#2196F3',
        icon: 'mdi-account',
        maxDays: 5,
        carryOver: false,
      },
      {
        id: 'maternity',
        name: 'Maternity Leave',
        color: '#E91E63',
        icon: 'mdi-baby-carriage',
        maxDays: 90,
        carryOver: false,
      },
      {
        id: 'paternity',
        name: 'Paternity Leave',
        color: '#9C27B0',
        icon: 'mdi-baby-face',
        maxDays: 14,
        carryOver: false,
      },
    ],
    policies: {
      maxAdvanceBooking: 365,
      minNotice: 1,
      maxConsecutiveDays: 30,
      requireApproval: true,
      autoApprovalLimit: 0,
    },
    workingDays: [1, 2, 3, 4, 5], // Monday to Friday
    fiscalYearStart: '01-01', // January 1st
  },
  notifications: {
    channels: {
      email: {
        enabled: true,
        templates: {
          leaveRequested: 'leave-requested',
          leaveApproved: 'leave-approved',
          leaveRejected: 'leave-rejected',
        },
      },
      push: {
        enabled: true,
        vapidKey: process.env.NUXT_PUBLIC_VAPID_KEY,
      },
      sms: {
        enabled: false,
        provider: 'twilio',
      },
    },
    events: {
      leaveRequested: true,
      leaveApproved: true,
      leaveRejected: true,
      leaveCancelled: true,
      reminderDue: true,
      balanceUpdated: false,
    },
  },
  integrations: {
    calendar: {
      provider: null,
      syncEnabled: false,
    },
    slack: {
      enabled: false,
      webhookUrl: process.env.NUXT_SLACK_WEBHOOK_URL,
      channels: ['#general', '#hr'],
    },
    teams: {
      enabled: false,
      webhookUrl: process.env.NUXT_TEAMS_WEBHOOK_URL,
    },
  },
  development: {
    debug: process.env.NODE_ENV === 'development',
    mockApi: process.env.NUXT_MOCK_API === 'true',
    showDevTools: process.env.NODE_ENV === 'development',
    logLevel: (process.env.NUXT_LOG_LEVEL as any) || 'info',
  },
};

// Export individual config sections for easier imports
export const themeConfig = appConfig.theme;
export const apiConfig = appConfig.api;
export const featureConfig = appConfig.features;
export const uiConfig = appConfig.ui;
export const securityConfig = appConfig.security;
export const leaveConfig = appConfig.leave;
export const notificationConfig = appConfig.notifications;
export const integrationConfig = appConfig.integrations;
export const devConfig = appConfig.development;

export default appConfig;
