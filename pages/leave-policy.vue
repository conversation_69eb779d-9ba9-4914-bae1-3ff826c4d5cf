<template>
  <v-app>
    <!-- Navigation Header -->
    <v-app-bar
      app
      color="white"
      elevation="0"
      height="80"
      class="px-6"
    >
      <v-container fluid class="pa-0">
        <v-row align="center" justify="space-between" no-gutters>
          <!-- Logo Section -->
          <v-col cols="auto">
            <v-row align="center" no-gutters class="ga-8">
              <!-- Logo -->
              <v-col cols="auto">
                <v-row align="center" no-gutters class="ga-3">
                  <v-col cols="auto">
                    <div class="logo-icon">
                      <v-icon color="primary" size="32">mdi-calendar-check</v-icon>
                    </div>
                  </v-col>
                  <v-col cols="auto">
                    <span class="text-h5 font-weight-bold text-grey-900">Leave</span>
                  </v-col>
                </v-row>
              </v-col>
              
              <!-- Page Title -->
              <v-col cols="auto">
                <span class="text-h6 font-weight-bold text-grey-700">Leave Policy</span>
              </v-col>
            </v-row>
          </v-col>

          <!-- Search and User Section -->
          <v-col cols="auto">
            <v-row align="center" no-gutters class="ga-6">
              <!-- Search Bar -->
              <v-col cols="auto">
                <v-text-field
                  placeholder="Search …"
                  variant="outlined"
                  density="compact"
                  prepend-inner-icon="mdi-magnify"
                  hide-details
                  class="search-field"
                  style="width: 336px;"
                />
              </v-col>
              
              <!-- Location Selector -->
              <v-col cols="auto">
                <v-btn
                  variant="text"
                  class="text-grey-600 font-weight-bold"
                  prepend-icon="mdi-domain"
                  append-icon="mdi-chevron-down"
                >
                  New York
                </v-btn>
              </v-col>

              <!-- User Profile -->
              <v-col cols="auto">
                <v-btn
                  variant="text"
                  class="text-grey-600 font-weight-bold"
                  prepend-icon="mdi-account"
                >
                  Adrain Nader
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- Main Content -->
    <v-main class="bg-grey-50">
      <v-container fluid class="pa-6" style="max-width: 1400px;">
        <v-row justify="center">
          <v-col cols="12">
            <v-row class="ga-5">
              <!-- Public Holidays Section -->
              <v-col cols="12">
                <PolicySection
                  title="Public Holidays"
                  :show-toggle="false"
                  :show-allowance="false"
                  :items="publicHolidays"
                  :headers="publicHolidaysHeaders"
                />
              </v-col>

              <!-- Deductible Leaves Section -->
              <v-col cols="12">
                <PolicySection
                  title="Deductible Leaves"
                  :show-toggle="true"
                  :toggle-value="true"
                  :show-allowance="true"
                  :items="deductibleLeaves"
                  :headers="deductibleLeavesHeaders"
                />
              </v-col>

              <!-- Non-deductible Leaves Section -->
              <v-col cols="12">
                <PolicySection
                  title="Non-deductible Leaves"
                  :show-toggle="false"
                  :show-allowance="false"
                  :items="nonDeductibleLeaves"
                  :headers="nonDeductibleLeavesHeaders"
                />
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Bottom Navigation -->
    <v-bottom-navigation
      app
      height="68"
      class="bottom-nav"
    >
      <v-btn value="home" class="nav-btn">
        <v-icon color="white">mdi-home</v-icon>
      </v-btn>

      <v-btn value="calendar" class="nav-btn" to="/calendar">
        <v-icon color="white">mdi-calendar</v-icon>
      </v-btn>

      <v-btn value="users" class="nav-btn">
        <v-icon color="white">mdi-account-group</v-icon>
      </v-btn>

      <v-btn value="messages" class="nav-btn">
        <v-icon color="white">mdi-message</v-icon>
      </v-btn>

      <v-btn value="policy" class="active-nav-item">
        <v-icon color="primary">mdi-file-document</v-icon>
        <span class="text-primary font-weight-medium">Leave Policy</span>
      </v-btn>

      <v-btn value="settings" class="nav-btn">
        <v-icon color="white">mdi-cog</v-icon>
      </v-btn>
    </v-bottom-navigation>
  </v-app>
</template>

<script setup lang="ts">
// Sample data for the policy sections
const publicHolidays = ref([
  {
    name: 'Deductible Leaves',
    description: '14 days of paid leave per year, but newspaper workers may not be eligible.',
    requiresApproval: 'Yes',
    label: 'Default'
  },
  {
    name: 'Sick leaves',
    description: 'Employees may need to provide a doctor\'s note to receive sick leave.',
    requiresApproval: 'Yes',
    label: 'Default'
  }
])

const deductibleLeaves = ref([
  {
    name: 'Annual/paid leaves',
    description: '14 days of paid leave per year, but newspaper workers may not be eligible.',
    requiresApproval: 'Yes',
    allowance: '24 days / Year',
    label: 'Default'
  },
  {
    name: 'Sick leaves',
    description: 'Employees may need to provide a doctor\'s note to receive sick leave.',
    requiresApproval: 'Yes',
    allowance: '24 days / Year',
    label: 'Default'
  }
])

const nonDeductibleLeaves = ref([
  {
    name: 'Maternity Leave',
    description: 'Paid maternity leave for eligible employees.',
    requiresApproval: 'Yes',
    label: 'Default'
  }
])

const publicHolidaysHeaders = [
  { title: 'Name', key: 'name' },
  { title: 'Description', key: 'description' },
  { title: 'Requires Approval', key: 'requiresApproval' }
]

const deductibleLeavesHeaders = [
  { title: 'Name', key: 'name' },
  { title: 'Description', key: 'description' },
  { title: 'Requires Approval', key: 'requiresApproval' },
  { title: 'Allowance', key: 'allowance' }
]

const nonDeductibleLeavesHeaders = [
  { title: 'Name', key: 'name' },
  { title: 'Description', key: 'description' },
  { title: 'Requires Approval', key: 'requiresApproval' }
]
</script>

<style scoped>
.search-field :deep(.v-field) {
  border-radius: 35px;
  border-color: #F5F5F5;
}

.search-field :deep(.v-field__input) {
  padding-left: 13px;
  padding-right: 13px;
}

.bottom-nav {
  background: rgba(250, 106, 106, 0.9) !important;
  backdrop-filter: blur(44px);
  box-shadow: 9px 10px 40.7px 0px rgba(0, 0, 0, 0.09);
}

.nav-btn {
  color: white !important;
}

.active-nav-item {
  background: white !important;
  border-radius: 12px !important;
  margin: 10px 6px !important;
  height: 52px !important;
  min-width: 154px !important;
  flex-direction: column !important;
  gap: 2px !important;
}

.logo-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
