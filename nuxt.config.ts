// https://nuxt.com/docs/api/configuration/nuxt-config
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify'
import { fileURLToPath } from 'node:url'
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  css: ['~/assets/main.scss'],

  // Runtime configuration
  runtimeConfig: {
    // Private keys (only available on server-side)
    jwtSecret: process.env.NUXT_JWT_SECRET,
    databaseUrl: process.env.NUXT_DATABASE_URL,
    smtpHost: process.env.NUXT_SMTP_HOST,
    smtpPort: process.env.NUXT_SMTP_PORT,
    smtpUser: process.env.NUXT_SMTP_USER,
    smtpPass: process.env.NUXT_SMTP_PASS,
    redisUrl: process.env.NUXT_REDIS_URL,
    slackWebhookUrl: process.env.NUXT_SLACK_WEBHOOK_URL,
    teamsWebhookUrl: process.env.NUXT_TEAMS_WEBHOOK_URL,
    storageProvider: process.env.NUXT_STORAGE_PROVIDER,
    s3Bucket: process.env.NUXT_S3_BUCKET,
    s3Region: process.env.NUXT_S3_REGION,
    s3AccessKey: process.env.NUXT_S3_ACCESS_KEY,
    s3SecretKey: process.env.NUXT_S3_SECRET_KEY,
    mockApi: process.env.NUXT_MOCK_API,
    logLevel: process.env.NUXT_LOG_LEVEL,

    // Public keys (exposed to client-side)
    public: {
      appUrl: process.env.NUXT_PUBLIC_APP_URL || 'http://localhost:3000',
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
      vapidKey: process.env.NUXT_PUBLIC_VAPID_KEY,
    }
  },

  build: {
    transpile: ['vuetify'],
  },
  modules: [
    (_options, nuxt) => {
      nuxt.hooks.hook('vite:extendConfig', (config) => {
        // @ts-expect-error
        config.plugins.push(vuetify({ autoImport: true }))
      })
    },
  ],
  vite: {
    vue: {
      template: {
        transformAssetUrls,
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('.', import.meta.url)),
        
      },
    },
  },
  typescript: {
    tsConfig: {
      compilerOptions: {
        paths: {
          '@/*': ['../*'],
        },
      },
    },
  },
})
