// Composable for accessing app configuration
import { appConfig, type AppConfig } from '~/config/app.config'

/**
 * Composable to access app configuration
 * Provides reactive access to all app configuration values
 */
export const useLeaveConfig = () => {
  // Make config reactive
  const config = reactive({ ...appConfig })

  // Computed properties for commonly used config sections
  const app = computed(() => config.app)
  const theme = computed(() => config.theme)
  const api = computed(() => config.api)
  const features = computed(() => config.features)
  const ui = computed(() => config.ui)
  const security = computed(() => config.security)
  const leave = computed(() => config.leave)
  const notifications = computed(() => config.notifications)
  const integrations = computed(() => config.integrations)
  const development = computed(() => config.development)

  // Helper functions
  const getApiUrl = (endpoint: string) => {
    return `${config.api.baseUrl}${endpoint}`
  }

  const getLeaveTypeById = (id: string) => {
    return config.leave.types.find(type => type.id === id)
  }

  const isFeatureEnabled = (feature: keyof AppConfig['features']) => {
    return config.features[feature] === true
  }

  const getThemeColor = (colorName: keyof AppConfig['theme']['colors']) => {
    return config.theme.colors[colorName]
  }

  const getFontFamily = (type: 'primary' | 'secondary' = 'primary') => {
    return config.theme.typography.fontFamily[type]
  }

  const getTypographySize = (size: keyof AppConfig['theme']['typography']['sizes']) => {
    return config.theme.typography.sizes[size]
  }

  const isNotificationEnabled = (event: keyof AppConfig['notifications']['events']) => {
    return config.notifications.events[event]
  }

  const isIntegrationEnabled = (integration: keyof AppConfig['integrations']) => {
    const integrationConfig = config.integrations[integration]
    if (typeof integrationConfig === 'boolean') {
      return integrationConfig
    }
    if (typeof integrationConfig === 'object' && integrationConfig !== null) {
      return 'enabled' in integrationConfig ? integrationConfig.enabled : 'syncEnabled' in integrationConfig ? integrationConfig.syncEnabled : false
    }
    return false
  }

  const isDevelopment = computed(() => config.development.debug)
  const isMockApiEnabled = computed(() => config.development.mockApi)

  // Update config (useful for runtime configuration changes)
  const updateConfig = (updates: Partial<AppConfig>) => {
    Object.assign(config, updates)
  }

  // Environment-specific helpers
  const getEnvironmentConfig = () => {
    return {
      isDevelopment: isDevelopment.value,
      isMockApi: isMockApiEnabled.value,
      logLevel: config.development.logLevel,
      showDevTools: config.development.showDevTools,
    }
  }

  return {
    // Full config
    config: readonly(config),
    
    // Config sections
    app,
    theme,
    api,
    features,
    ui,
    security,
    leave,
    notifications,
    integrations,
    development,
    
    // Helper functions
    getApiUrl,
    getLeaveTypeById,
    isFeatureEnabled,
    getThemeColor,
    getFontFamily,
    getTypographySize,
    isNotificationEnabled,
    isIntegrationEnabled,
    updateConfig,
    getEnvironmentConfig,
    
    // Computed helpers
    isDevelopment,
    isMockApiEnabled,
  }
}

// Type-safe config access
export type UseLeaveConfigReturn = ReturnType<typeof useLeaveConfig>
