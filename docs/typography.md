# Typography System Documentation

This document outlines the typography system implemented for the Leave Management System based on the TT Hoves font family from the Figma design.

## Font Family

### Primary Font: TT Hoves
- **Regular (400)**: For body text and general content
- **Medium (500)**: For emphasized text and medium headings
- **DemiBold (600)**: For strong emphasis and important headings
- **Bold (700)**: For maximum emphasis and large headings

### Fallback Fonts
```css
font-family: 'TT Hoves', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
```

## Typography Scale

### Website Headings (Large Scale)
- **H1 Website**: 100px, Demi<PERSON><PERSON> (600), line-height: 1.1, letter-spacing: -2%
- **H2 Website**: 80px, Demi<PERSON><PERSON> (600), line-height: 1.1, letter-spacing: -2%
- **H3 Website**: 64px, <PERSON><PERSON><PERSON><PERSON> (600), line-height: 1.2, letter-spacing: -2%
- **H4 Website**: 48px, Demi<PERSON><PERSON> (600), line-height: 1.2, letter-spacing: -1%
- **H5 Website**: 32px, DemiBold (600), line-height: 1.5

### Dashboard Headings (Application Scale)
- **H1 Dashboard**: 48px, Medium (500), line-height: 1.5, letter-spacing: -1%
- **H2 Dashboard**: 40px, Medium (500), line-height: 1.5, letter-spacing: -1%
- **H3 Dashboard**: 32px, DemiBold (600), line-height: 1.5, letter-spacing: -1%
- **H4 Dashboard**: 24px, DemiBold (600), line-height: 1.5, letter-spacing: -1%

### Body Text
- **24px Bold**: DemiBold (600), line-height: 1.4, letter-spacing: -1%
- **24px Medium**: Medium (500), line-height: 1.4, letter-spacing: -1%
- **24px Regular**: Regular (400), line-height: 1.4, letter-spacing: -1%
- **20px Bold**: DemiBold (600), line-height: 1.5, letter-spacing: -1%
- **20px Medium**: Medium (500), line-height: 1.5
- **20px Regular**: Regular (400), line-height: 1.5, letter-spacing: -1%
- **18px Bold**: DemiBold (600), line-height: 1.6, letter-spacing: -1%
- **18px Medium**: Medium (500), line-height: 1.6, letter-spacing: -1%
- **18px Regular**: Regular (400), line-height: 1.6
- **16px Bold**: DemiBold (600), line-height: 1.6, letter-spacing: -1%
- **16px Medium**: Medium (500), line-height: 1.6, letter-spacing: -2%
- **16px Regular**: Regular (400), line-height: 1.6
- **14px Bold**: DemiBold (600), line-height: 1.6
- **14px Medium**: Medium (500), line-height: 1.6
- **14px Regular**: Regular (400), line-height: 1.6, letter-spacing: 1%
- **12px Bold**: DemiBold (600), line-height: 1.6
- **12px Medium**: Medium (500), line-height: 1.6
- **12px Regular**: Regular (400), line-height: 1.6, letter-spacing: 1%

### Special Text Styles
- **Button 24px**: 24px, DemiBold (600), line-height: 1.5, letter-spacing: -1%
- **Title 1**: 32px, Medium (500), line-height: 1.3, letter-spacing: -1%
- **Title 2**: 28px, DemiBold (600), line-height: 1.5
- **Title 3**: 18px, DemiBold (600), line-height: 1.45, letter-spacing: -1%
- **Caption**: 16px, Medium (500), line-height: 1.5, letter-spacing: -1%
- **Caption Small**: 14px, Medium (500), line-height: 1.4, letter-spacing: -1%

## CSS Classes

### Heading Classes
```css
.text-h1-website    /* 100px DemiBold */
.text-h2-website    /* 80px DemiBold */
.text-h3-website    /* 64px DemiBold */
.text-h4-website    /* 48px DemiBold */
.text-h5-website    /* 32px DemiBold */

.text-h1-dashboard  /* 48px Medium */
.text-h2-dashboard  /* 40px Medium */
.text-h3-dashboard  /* 32px DemiBold */
.text-h4-dashboard  /* 24px DemiBold */
```

### Body Text Classes
```css
.text-body-24-bold     .text-body-24-medium     .text-body-24-regular
.text-body-20-bold     .text-body-20-medium     .text-body-20-regular
.text-body-18-bold     .text-body-18-medium     .text-body-18-regular
.text-body-16-bold     .text-body-16-medium     .text-body-16-regular
.text-body-14-bold     .text-body-14-medium     .text-body-14-regular
.text-body-12-bold     .text-body-12-medium     .text-body-12-regular
```

### Special Classes
```css
.text-button-24       /* Button text */
.text-title-1         /* Large title */
.text-title-2         /* Medium title */
.text-title-3         /* Small title */
.text-caption         /* Caption text */
.text-caption-small   /* Small caption */
```

### Font Weight Utilities
```css
.font-regular    /* 400 */
.font-medium     /* 500 */
.font-demibold   /* 600 */
.font-bold       /* 700 */
```

## Usage Examples

### Vue Components
```vue
<template>
  <!-- Page heading -->
  <h1 class="text-h1-dashboard text-primary">Dashboard</h1>
  
  <!-- Section heading -->
  <h2 class="text-h3-dashboard text-primary mb-4">Recent Activity</h2>
  
  <!-- Card title -->
  <v-card-title class="text-body-18-bold text-primary">
    Leave Request Details
  </v-card-title>
  
  <!-- Body text -->
  <p class="text-body-16-regular">
    Your leave request has been submitted and is pending approval.
  </p>
  
  <!-- Button with custom typography -->
  <v-btn color="primary" class="text-button-24">
    Submit Request
  </v-btn>
  
  <!-- Caption text -->
  <div class="text-caption text-grey-600">
    Last updated 2 hours ago
  </div>
</template>
```

### SCSS Usage
```scss
// Import typography variables
@import '~/assets/fonts.scss';

.custom-heading {
  font-family: $font-family-primary;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.5;
}
```

## Responsive Behavior

### Mobile (≤768px)
- Website headings scale down by ~35%
- Dashboard headings scale down by ~25%
- Body text remains consistent

### Small Mobile (≤480px)
- Website headings scale down by ~50%
- Dashboard headings scale down by ~40%
- Body text remains consistent

## Performance Considerations

1. **Font Display**: Uses `font-display: swap` for optimal loading
2. **Fallback Fonts**: System fonts provide immediate text rendering
3. **Font Formats**: WOFF2, WOFF, and TTF for broad browser support
4. **Preloading**: Consider preloading critical font weights

## Accessibility

1. **Contrast**: All text meets WCAG 2.1 AA contrast requirements
2. **Scalability**: Typography scales properly with browser zoom
3. **Readability**: Line heights optimized for reading comfort
4. **Fallbacks**: System fonts ensure text is always readable

## Implementation Status

- ✅ Font face declarations created
- ✅ Typography utility classes implemented
- ✅ Vuetify integration configured
- ✅ Global font family applied
- ✅ Responsive scaling implemented
- ⚠️ Font files need to be added to `assets/fonts/`
- ✅ Documentation complete

## Next Steps

1. Obtain TT Hoves font files from design team or TypeType
2. Place font files in `assets/fonts/` directory
3. Test font loading across different browsers
4. Optimize font loading performance if needed
