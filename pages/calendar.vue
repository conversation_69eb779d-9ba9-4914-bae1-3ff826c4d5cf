<template>
  <v-app>
    <!-- Navigation Header -->
    <v-app-bar
      app
      color="white"
      elevation="0"
      height="80"
      class="border-b"
    >
      <v-container fluid class="px-6">
        <v-row align="center" justify="space-between" no-gutters>
          <!-- Left Section: Logo and Title -->
          <v-col cols="auto">
            <v-row align="center" no-gutters class="gap-4">
              <!-- Logo -->
              <v-col cols="auto">
                <v-row align="center" no-gutters class="gap-3">
                  <v-avatar size="32" color="#FA4545" class="rounded-lg">
                    <v-icon color="white" size="20">mdi-calendar</v-icon>
                  </v-avatar>
                  <span class="text-h6 font-weight-bold text-black">Leave</span>
                </v-row>
              </v-col>
              
              <!-- Page Title -->
              <v-col cols="auto" class="ml-8">
                <h1 class="text-h5 font-weight-bold text-grey-darken-3">Calendar</h1>
              </v-col>
            </v-row>
          </v-col>

          <!-- Right Section: Search, Location, Profile -->
          <v-col cols="auto">
            <v-row align="center" no-gutters class="gap-6">
              <!-- Search Bar -->
              <v-col cols="auto">
                <v-text-field
                  placeholder="Search"
                  variant="outlined"
                  density="compact"
                  hide-details
                  prepend-inner-icon="mdi-magnify"
                  class="search-field"
                  style="width: 300px;"
                />
              </v-col>

              <!-- Location Selector -->
              <v-col cols="auto">
                <v-row align="center" no-gutters class="gap-1">
                  <v-icon color="#ADB8CC" size="16">mdi-map-marker</v-icon>
                  <span class="text-body-2 text-grey-darken-1">New York</span>
                  <v-icon color="#ADB8CC" size="16">mdi-chevron-down</v-icon>
                </v-row>
              </v-col>

              <!-- User Profile -->
              <v-col cols="auto">
                <v-row align="center" no-gutters class="gap-2">
                  <v-avatar size="30">
                    <v-img src="https://via.placeholder.com/30x30/4A90E2/FFFFFF?text=AN" />
                  </v-avatar>
                  <span class="text-body-2 font-weight-medium">Adrain Nader</span>
                  <v-icon color="#ADB8CC" size="16">mdi-account</v-icon>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-container>
    </v-app-bar>

    <!-- Main Content -->
    <v-main class="bg-grey-lighten-5">
      <v-container fluid class="pa-6">
        <v-row no-gutters>
          <!-- Calendar Content -->
          <v-col cols="9">
            <CalendarGrid />
          </v-col>
          
          <!-- Sidebar -->
          <v-col cols="3" class="pl-6">
            <CalendarSidebar />
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Bottom Navigation -->
    <v-bottom-navigation
      v-model="activeTab"
      color="white"
      height="68"
      class="bottom-nav"
    >
      <v-btn value="dashboard" class="nav-item">
        <v-icon>mdi-view-dashboard</v-icon>
        <span>Dashboard</span>
      </v-btn>

      <v-btn value="leave-policy" class="nav-item" to="/leave-policy">
        <v-icon>mdi-file-document</v-icon>
        <span>Leave Policy</span>
      </v-btn>

      <v-btn value="calendar" class="nav-item active">
        <v-icon>mdi-calendar</v-icon>
        <span>Calendar</span>
      </v-btn>

      <v-btn value="team" class="nav-item">
        <v-icon>mdi-account-group</v-icon>
        <span>Team</span>
      </v-btn>

      <v-btn value="settings" class="nav-item">
        <v-icon>mdi-cog</v-icon>
        <span>Settings</span>
      </v-btn>
    </v-bottom-navigation>
  </v-app>
</template>

<script setup>
import { ref } from 'vue'

const activeTab = ref('calendar')
</script>

<style scoped>
.border-b {
  border-bottom: 1px solid #F5F5F5;
}

.gap-1 {
  gap: 4px;
}

.gap-2 {
  gap: 8px;
}

.gap-3 {
  gap: 12px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

.search-field :deep(.v-field) {
  border-radius: 12px;
  background-color: #FAFAFA;
  border: 1px solid #E5E5E5;
}

.search-field :deep(.v-field__input) {
  padding: 8px 16px;
  font-size: 14px;
}

.bottom-nav {
  background: linear-gradient(90deg, #FA4545 0%, #FF6B6B 100%);
  backdrop-filter: blur(44px);
  border-radius: 20px 20px 0 0;
}

.nav-item {
  color: rgba(255, 255, 255, 0.7) !important;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.nav-item.active {
  background-color: white !important;
  color: #FA4545 !important;
  border-radius: 12px;
  margin: 8px;
}

.nav-item.active .v-icon {
  color: #FA4545 !important;
}

.nav-item .v-icon {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 2px;
}
</style>
